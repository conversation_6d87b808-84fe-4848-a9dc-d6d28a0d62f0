package com.userbff.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

@Slf4j
public class TestResourceUtil {
    private static final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());

    public static String loadResourceAsString(String resourcePath) {
        try {
            ClassPathResource resource = new ClassPathResource(resourcePath);
            return StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("Failed to load resource: {}", resourcePath, e);
            throw new RuntimeException("Failed to load resource: " + resourcePath, e);
        }
    }

    public static <T> T loadResourceAsObject(String resourcePath, Class<T> clazz) {
        try {
            String json = loadResourceAsString(resourcePath);
            return objectMapper.readValue(json, clazz);
        } catch (IOException e) {
            log.error("Failed to parse resource as object: {}", resourcePath, e);
            throw new RuntimeException("Failed to parse resource as object: " + resourcePath, e);
        }
    }

    public static <T> T loadResourceAsObjectAndReplaceIds(String resourcePath, Class<T> clazz) {
        try {
            String json = loadResourceAsString(resourcePath);
            json = json.replace("test-message-id", UUID.randomUUID().toString())
                    .replace("test-user-id", UUID.randomUUID().toString())
                    .replace("test-uzum-id", UUID.randomUUID().toString())
                    .replace("test-abs-customer-id", UUID.randomUUID().toString());
            return objectMapper.readValue(json, clazz);
        } catch (IOException e) {
            log.error("Failed to parse resource as object: {}", resourcePath, e);
            throw new RuntimeException("Failed to parse resource as object: " + resourcePath, e);
        }
    }
}
