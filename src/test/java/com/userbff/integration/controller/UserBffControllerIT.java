package com.userbff.integration.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.userbff.client.ApelsinClient;
import com.userbff.client.KYCClient;
import com.userbff.client.UserRestrictionsClient;
import com.userbff.client.UserVerificationClient;
import com.userbff.entity.UserDecisionContext;
import com.userbff.enums.ScenarioStatus;
import com.userbff.enums.ScenarioType;
import com.userbff.integration.BaseIntegrationTest;
import com.userbff.model.UserBffResponseDto;
import com.userbff.model.address.AddressUpdateDto;
import com.userbff.model.common.ResponseData;
import com.userbff.model.kyc.KYCBaseResponseDto;
import com.userbff.model.onboarding.CheckDto;
import com.userbff.model.onboarding.IdentificationState;
import com.userbff.model.restrictions.RestrictionsResponseDto;
import com.userbff.model.user.UserInfoDto;
import com.userbff.model.user_session.CodeDto;
import com.userbff.model.user_session.ExternalFlags;
import com.userbff.model.user_session.UserSessionRequestDto;
import com.userbff.model.verification.VerifyUserResponseDto;
import com.userbff.repository.UserDecisionContextRepository;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.List;

import static com.userbff.enums.HeaderConstants.API_VERSION;
import static com.userbff.enums.HeaderConstants.DEVICE_ID;
import static com.userbff.enums.HeaderConstants.X_API_KEY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Tag("integration")
class UserBffControllerIT extends BaseIntegrationTest {
    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private UserDecisionContextRepository repository;
    @Autowired
    private ObjectMapper objectMapper;
    @MockBean
    private ApelsinClient apelsinClient;
    @MockBean
    private UserVerificationClient userVerificationClient;
    @MockBean
    private UserRestrictionsClient userRestrictionsClient;
    @MockBean
    private KYCClient kycClient;

    private static final String TEST_API_VERSION = "1";
    private static final String TEST_API_KEY = "secretik";
    private static final String TEST_DEVICE_ID = "test-device-id";

    @BeforeEach
    void setUp() {
        repository.deleteAll();
    }

    @Test
    void getUserRestrictions_WhenHasRestrictions_ReturnsRestrictions() throws Exception {
        List<UserDecisionContext> userDecisionContexts = repository.findAll();
        assertTrue(userDecisionContexts.isEmpty());

        CodeDto codeDto = Instancio.create(CodeDto.class);
        UserSessionRequestDto requestDto = new UserSessionRequestDto(
                new ExternalFlags(true),
                codeDto,
                new AddressUpdateDto(1, 1, "address"),
                new CheckDto(true, true, IdentificationState.SUCCESS)
        );
        UserInfoDto userInfoDto = Instancio.create(UserInfoDto.class);
        long userId = userInfoDto.userId();
        when(apelsinClient.getUser()).thenReturn(ResponseEntity.ok(new ResponseData<>(userInfoDto)));
        when(apelsinClient.updateUserActivity()).thenReturn(ResponseEntity.ok().build());
        CheckDto checkDto = new CheckDto(true, true, IdentificationState.SUCCESS);
        when(apelsinClient.getOnboardingCheck()).thenReturn(ResponseEntity.ok(new ResponseData<>(checkDto)));
        when(userVerificationClient.getVerificationStatus(any(), any())).thenReturn(ResponseEntity.ok(Boolean.FALSE));
        VerifyUserResponseDto verifyUserResponseDto = new VerifyUserResponseDto(true, "verified");
        when(userVerificationClient.verify(any())).thenReturn(ResponseEntity.ok(verifyUserResponseDto));
        RestrictionsResponseDto restrictionsResponseDto = new RestrictionsResponseDto(
                true,
                true,
                LocalDate.now().plusDays(7),
                true,
                true,
                "customerDataProcessing",
                "success"
        );
        when(userRestrictionsClient.getUserRestrictions(String.valueOf(userId)))
                .thenReturn(ResponseEntity.ok(restrictionsResponseDto));
//        when(kycClient.updateRegistrationAddress(any()))
//                .thenReturn(ResponseEntity.ok(new KYCBaseResponseDto("RC20000", "")));

        MvcResult mvcResult = mockMvc.perform(post("/api/user-bff/user-session")
                        .header(API_VERSION, TEST_API_VERSION)
                        .header(X_API_KEY, TEST_API_KEY)
                        .header(DEVICE_ID, TEST_DEVICE_ID)
                        .content(objectMapper.writeValueAsString(requestDto))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();

        String jsonResponse = mvcResult.getResponse().getContentAsString();
        jsonResponse = new String(jsonResponse.getBytes("ISO-8859-1"), StandardCharsets.UTF_8);

        ResponseData<UserBffResponseDto> response = objectMapper.readValue(
                jsonResponse, new TypeReference<ResponseData<UserBffResponseDto>>() {
                }
        );
        UserBffResponseDto data = response.data();

        assertNotNull(data);
        // user
        assertEquals(userInfoDto, data.user());
        // meta
        assertNotNull(data.metaInfo());
        assertTrue(data.metaInfo().isVerificationPassed());
        assertEquals(IdentificationState.SUCCESS, data.metaInfo().identificationState());
        assertTrue(data.metaInfo().isDocumentValid());
        assertFalse(data.metaInfo().isCardFlowRequired());
        // notification
        assertNotNull(data.notification());
        assertEquals(ScenarioType.PASSPORT_EXPIRED_RESIDENT, data.notification().type());
        assertEquals(ScenarioType.PASSPORT_EXPIRED_RESIDENT.getTitle(), data.notification().title());
        assertEquals(ScenarioType.PASSPORT_EXPIRED_RESIDENT.getSubTitle(), data.notification().subtitle());
        assertEquals(ScenarioType.PASSPORT_EXPIRED_RESIDENT.getButtons(), data.notification().buttons());
        assertEquals(ScenarioType.PASSPORT_EXPIRED_RESIDENT.getIcon(), data.notification().icon());
        assertEquals(ScenarioStatus.NOT_STARTED, data.notification().status());
        assertTrue(data.notification().closable());
        assertTrue(data.notification().showOnStart());
        // additional check
        assertNotNull(data.additionalCheck());
        assertEquals("MY_ID", data.additionalCheck().biometryProvider());

        userDecisionContexts = repository.findAll();
        assertEquals(1, userDecisionContexts.size());
        assertEquals(String.valueOf(userId), userDecisionContexts.get(0).getUserId());
    }

    @Test
    void getUserRestrictions_WhenNoApiKey_ReturnsUnauthorized() throws Exception {
        mockMvc.perform(get("/user-bff/{userId}", "test-user"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void getUserRestrictions_WhenInvalidApiKey_ReturnsUnauthorized() throws Exception {
        mockMvc.perform(get("/user-bff/{userId}", "test-user")
                        .header(X_API_KEY, "invalid-key"))
                .andExpect(status().isUnauthorized());
    }
}
