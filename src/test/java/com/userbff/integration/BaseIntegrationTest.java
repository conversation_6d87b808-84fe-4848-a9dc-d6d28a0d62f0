package com.userbff.integration;

import com.userbff.UserBffApplication;
import com.userbff.model.KafkaEvent;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.support.TestPropertySourceUtils;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.wait.strategy.Wait;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import java.time.Duration;

@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        classes = {UserBffApplication.class})
@DirtiesContext
@Testcontainers
@ContextConfiguration(initializers = {BaseIntegrationTest.Initializer.class})
@AutoConfigureMockMvc
@Slf4j
public abstract class BaseIntegrationTest {
    @Autowired
    protected KafkaTemplate<String, KafkaEvent> kafkaTemplateTest;

    @Container
    private static final PostgreSQLContainer<?> POSTGRESQL_CONTAINER = new PostgreSQLContainer<>(
            DockerImageName.parse("postgres:14.9-alpine"))
            .withDatabaseName("user_restriction_test")
            .withUsername("test")
            .withPassword("test")
            .withStartupTimeout(Duration.ofMinutes(5));

    @Container
    private static final KafkaContainer KAFKA_CONTAINER = new KafkaContainer(
            DockerImageName.parse("confluentinc/cp-kafka:7.4.0"))
            .withStartupTimeout(Duration.ofMinutes(5))
            .withEnv("KAFKA_AUTO_CREATE_TOPICS_ENABLE", "true")
            .waitingFor(Wait.forListeningPort());

    @DynamicPropertySource
    static void registerPostgresProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", POSTGRESQL_CONTAINER::getJdbcUrl);
        registry.add("spring.datasource.username", POSTGRESQL_CONTAINER::getUsername);
        registry.add("spring.datasource.password", POSTGRESQL_CONTAINER::getPassword);
        registry.add("spring.datasource.driver-class-name", () -> "org.postgresql.Driver");
    }

    @BeforeAll
    static void setup() {
        POSTGRESQL_CONTAINER.start();
        KAFKA_CONTAINER.start();
    }

    static class Initializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

        @Override
        public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
            TestPropertySourceUtils.addInlinedPropertiesToEnvironment(
                    configurableApplicationContext,
                    "spring.datasource.url=" + POSTGRESQL_CONTAINER.getJdbcUrl(),
                    "spring.datasource.username=" + POSTGRESQL_CONTAINER.getUsername(),
                    "spring.datasource.password=" + POSTGRESQL_CONTAINER.getPassword(),
                    "spring.kafka.bootstrap-servers=" + KAFKA_CONTAINER.getBootstrapServers(),
                    "spring.kafka.consumer.auto-offset-reset=earliest"
            );
            log.info("Test properties applied to application context");
        }
    }

    protected void sendEvent(String topic, KafkaEvent eventMessage) {
        kafkaTemplateTest.send(topic, eventMessage)
                .thenAccept(result -> {
                    if (result != null) {
                        log.info("Successfully sent message to Kafka with id {}", eventMessage.getMessageId());
                    }
                })
                .exceptionally(ex -> {
                    log.error("Unable to send message to Kafka with id {}, cause: {}", eventMessage.getMessageId(), ex.getMessage());
                    return null;
                });
    }
}
