package com.userbff.integration.kafka.consumer;

import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.userbff.config.KafkaConfig;
import com.userbff.entity.UserDecisionContext;
import com.userbff.enums.UserFlag;
import com.userbff.integration.BaseIntegrationTest;
import com.userbff.model.restrictions.RestrictionsUpdateEvent;
import com.userbff.repository.UserDecisionContextRepository;
import com.userbff.util.TestResourceUtil;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Tag("integration")
class RestrictionsConsumerIT extends BaseIntegrationTest {
    @Autowired
    private UserDecisionContextRepository userDecisionContextRepository;

    private static final String RESTRICTIONS_UPDATE_EVENT_PATH = "events/restrictions-update-event.json";

    @BeforeEach
    void setUp() {
        userDecisionContextRepository.deleteAll();
    }

    @Test
    void shouldProcessDocumentUpdateEvent() {
        RestrictionsUpdateEvent event = TestResourceUtil.loadResourceAsObjectAndReplaceIds(
                RESTRICTIONS_UPDATE_EVENT_PATH, RestrictionsUpdateEvent.class);

        sendEvent(KafkaConfig.RESTRICTIONS_UPDATE_TOPIC, event);

        await().atMost(1, TimeUnit.MINUTES).untilAsserted(() -> {
            Optional<UserDecisionContext> userDecisionContextOpt =
                    userDecisionContextRepository.findAll().stream().findAny();
            assertTrue(userDecisionContextOpt.isPresent());

            var userDecisionContext = userDecisionContextOpt.get();
            assertEquals(
                    event.getBody().isResident(),
                    userDecisionContext.getUserFlags().contains(UserFlag.RESIDENT));
            assertEquals(
                    event.getBody().isActive(),
                    userDecisionContext.getUserFlags().contains(UserFlag.ACTIVE));
            assertEquals(event.getBody().expiryDate(), userDecisionContext.getExpiryDate());
            assertEquals(
                    event.getBody().hasRegisteredAddress(),
                    userDecisionContext.getUserFlags().contains(UserFlag.HAS_REGISTERED_ADDRESS));
            assertEquals(
                    event.getBody().requiresAddressUpdate(),
                    userDecisionContext.getUserFlags().contains(UserFlag.REQUIRES_ADDRESS_UPDATE));
        });
    }
}
