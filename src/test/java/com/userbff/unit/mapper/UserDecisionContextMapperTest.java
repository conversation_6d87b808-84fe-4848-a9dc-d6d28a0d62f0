package com.userbff.unit.mapper;

import com.userbff.entity.UserDecisionContext;
import com.userbff.enums.ScenarioFlag;
import com.userbff.enums.ScenarioStatus;
import com.userbff.enums.ScenarioType;
import com.userbff.enums.UserFlag;
import com.userbff.mapper.UserDecisionContextMapper;
import com.userbff.model.onboarding.CheckDto;
import com.userbff.model.onboarding.IdentificationState;
import com.userbff.model.restrictions.RestrictionsResponseDto;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

//@ExtendWith(MockitoExtension.class)
class UserDecisionContextMapperTest {
//    private final UserDecisionContextMapper mapper = new UserDecisionContextMapper();
//    private static final String USER_ID = "1";
//
//    @Test
//    void shouldMapBasicFields_whenAllDataIsValid() {
//        // given
//        LocalDate initialExpiry = LocalDate.of(2024, 5, 1);
//        UserDecisionContext context = new UserDecisionContext();
//        context.setExpiryDate(initialExpiry);
//        context.setScenarioStatus(ScenarioStatus.PENDING);
//
//        RestrictionsResponseDto restrictions = RestrictionsResponseDto.builder()
//                .isResident(true)
//                .isActive(true)
//                .hasRegisteredAddress(true)
//                .requiresAddressUpdate(false)
//                .expiryDate(initialExpiry)
//                .customerDataProcessing("none")
//                .build();
//
//        CheckDto onboardingResponse = new CheckDto(true, true, IdentificationState.SUCCESS);
//
//        // when
//        UserDecisionContext result = mapper.map(context, USER_ID, restrictions, onboardingResponse);
//
//        // then
//        assertEquals("1", result.getUserId());
//        assertEquals(initialExpiry, result.getExpiryDate());
//        assertEquals(initialExpiry, result.getExpiryDatePrev());
//        assertTrue(result.getUserFlags().contains(UserFlag.RESIDENT));
//        assertTrue(result.getUserFlags().contains(UserFlag.ACTIVE));
//        assertTrue(result.getUserFlags().contains(UserFlag.HAS_REGISTERED_ADDRESS));
//        assertFalse(result.getUserFlags().contains(UserFlag.REQUIRES_ADDRESS_UPDATE));
//    }
//
//    @Test
//    void shouldSetScenarioToPassportExpiredResident_whenExpiredAndNotActive() {
//        // given
//        UserDecisionContext context = new UserDecisionContext();
//        context.setExpiryDate(LocalDate.now().minusDays(10));
//
//        RestrictionsResponseDto restrictions = RestrictionsResponseDto.builder()
//                .isResident(true)
//                .isActive(false)
//                .hasRegisteredAddress(true)
//                .requiresAddressUpdate(false)
//                .expiryDate(LocalDate.now().minusDays(1))
//                .customerDataProcessing("none")
//                .build();
//
//        CheckDto onboardingResponse = new CheckDto(true, true, IdentificationState.SUCCESS);
//
//        // when
//        UserDecisionContext result = mapper.map(context, USER_ID, restrictions, onboardingResponse);
//
//        // then
//        assertEquals(ScenarioType.PASSPORT_EXPIRED_RESIDENT, result.getScenarioType());
//        assertTrue(result.getScenarioFlags().contains(ScenarioFlag.SHOW_ON_START));
//        assertEquals(ScenarioStatus.NOT_STARTED, result.getScenarioStatus());
//    }
//
//    @Test
//    void shouldSetScenarioToAddressUpdate_whenSoonExpiryAndMissingAddress() {
//        // given
//        UserDecisionContext context = new UserDecisionContext();
//        context.setExpiryDate(LocalDate.now().minusDays(1));
//
//        RestrictionsResponseDto restrictions = RestrictionsResponseDto.builder()
//                .isResident(true)
//                .isActive(true)
//                .hasRegisteredAddress(false)
//                .requiresAddressUpdate(true)
//                .expiryDate(LocalDate.now().plusDays(10))
//                .customerDataProcessing("none")
//                .build();
//
//        CheckDto onboardingResponse = new CheckDto(true, true, IdentificationState.SUCCESS);
//
//        // when
//        UserDecisionContext result = mapper.map(context, USER_ID, restrictions, onboardingResponse);
//
//        // then
//        assertEquals(ScenarioType.ADDRESS_NOT_FOUND, result.getScenarioType());
//        assertTrue(result.getScenarioFlags().contains(ScenarioFlag.SHOW_ON_START));
//        assertEquals(ScenarioStatus.NOT_STARTED, result.getScenarioStatus());
//    }
//
//    @Test
//    void shouldSetScenarioNone_whenExpiryFarInFuture_andValidAddress() {
//        // given
//        UserDecisionContext context = new UserDecisionContext();
//        context.setExpiryDate(LocalDate.now());
//
//        RestrictionsResponseDto restrictions = RestrictionsResponseDto.builder()
//                .isResident(true)
//                .isActive(true)
//                .hasRegisteredAddress(true)
//                .requiresAddressUpdate(false)
//                .expiryDate(LocalDate.now().plusDays(60))
//                .customerDataProcessing("none")
//                .build();
//
//        CheckDto onboardingResponse = new CheckDto(true, true, IdentificationState.SUCCESS);
//
//        // when
//        UserDecisionContext result = mapper.map(context, USER_ID, restrictions, onboardingResponse);
//
//        // then
//        assertEquals(ScenarioType.NONE, result.getScenarioType());
//        assertFalse(result.getScenarioFlags().contains(ScenarioFlag.SHOW_ON_START));
//        assertNull(result.getScenarioStatus());
//    }
//
//    @Test
//    void shouldSetScenarioServerError_whenIdentificationStateFailed() {
//        // given
//        UserDecisionContext context = new UserDecisionContext();
//        context.setExpiryDate(LocalDate.now());
//
//        RestrictionsResponseDto restrictions = RestrictionsResponseDto.builder()
//                .isResident(true)
//                .isActive(true)
//                .hasRegisteredAddress(true)
//                .requiresAddressUpdate(false)
//                .expiryDate(LocalDate.now().plusDays(60))
//                .customerDataProcessing("none")
//                .build();
//
//        CheckDto onboardingResponse = new CheckDto(true, true, IdentificationState.FAIL);
//
//        // when
//        UserDecisionContext result = mapper.map(context, USER_ID, restrictions, onboardingResponse);
//
//        // then
//        assertEquals(ScenarioType.SERVER_ERROR, result.getScenarioType());
//    }
//
//    @Test
//    void shouldSetScenarioValidationError_whenIdentificationStateLackOfData() {
//        // given
//        UserDecisionContext context = new UserDecisionContext();
//        context.setExpiryDate(LocalDate.now());
//
//        RestrictionsResponseDto restrictions = RestrictionsResponseDto.builder()
//                .isResident(true)
//                .isActive(true)
//                .hasRegisteredAddress(true)
//                .requiresAddressUpdate(false)
//                .expiryDate(LocalDate.now().plusDays(60))
//                .customerDataProcessing("none")
//                .build();
//
//        CheckDto onboardingResponse = new CheckDto(true, true, IdentificationState.LACK_OF_DATA);
//
//        // when
//        UserDecisionContext result = mapper.map(context, USER_ID, restrictions, onboardingResponse);
//
//        // then
//        assertEquals(ScenarioType.DATA_VALIDATION_ERROR, result.getScenarioType());
//    }
//
//    @Test
//    void shouldSetScenarioValidationError_whenIdentificationStateLackOfDataHold() {
//        // given
//        UserDecisionContext context = new UserDecisionContext();
//        context.setExpiryDate(LocalDate.now());
//
//        RestrictionsResponseDto restrictions = RestrictionsResponseDto.builder()
//                .isResident(true)
//                .isActive(true)
//                .hasRegisteredAddress(true)
//                .requiresAddressUpdate(false)
//                .expiryDate(LocalDate.now().plusDays(60))
//                .customerDataProcessing("none")
//                .build();
//
//        CheckDto onboardingResponse = new CheckDto(true, true, IdentificationState.LACK_OF_DATA_HOLD);
//
//        // when
//        UserDecisionContext result = mapper.map(context, USER_ID, restrictions, onboardingResponse);
//
//        // then
//        assertEquals(ScenarioType.DATA_VALIDATION_ERROR, result.getScenarioType());
//    }
}