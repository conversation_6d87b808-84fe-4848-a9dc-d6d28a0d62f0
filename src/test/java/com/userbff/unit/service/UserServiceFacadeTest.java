package com.userbff.unit.service;

import com.userbff.entity.UserDecisionContext;
import com.userbff.enums.Lang;
import com.userbff.enums.ScenarioFeedback;
import com.userbff.exception.BusinessLogicException;
import com.userbff.exception.ErrorCode;
import com.userbff.kafka.producer.NotificationProducer;
import com.userbff.model.UserBffResponseDto;
import com.userbff.model.address.AddressUpdateDto;
import com.userbff.model.onboarding.CheckDto;
import com.userbff.model.restrictions.RestrictionsResponseDto;
import com.userbff.model.user.UserInfoDto;
import com.userbff.model.user.UserState;
import com.userbff.model.user.UserType;
import com.userbff.model.user_session.CodeDto;
import com.userbff.model.user_session.ExternalFlags;
import com.userbff.model.user_session.UserSessionRequestDto;
import com.userbff.model.user_session.WarningActionRequestDto;
import com.userbff.model.verification.VerifyUserResponseDto;
import com.userbff.service.UserDecisionContextService;
import com.userbff.service.UserServiceFacade;
import com.userbff.service.kyc.KYCService;
import com.userbff.service.message.MessageService;
import com.userbff.service.onboarding.OnboardingService;
import com.userbff.service.restrictions.UserRestrictionsService;
import com.userbff.service.user.UserService;
import com.userbff.service.verification.UserVerificationService;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import uz.uzum.commons.starter.data.entity.apelsin.k_app.user.enums.UserRole;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UserServiceFacadeTest {
    @Mock
    private UserService userService;
    @Mock
    private UserVerificationService userVerificationService;
    @Mock
    private OnboardingService onboardingService;
    @Mock
    private UserRestrictionsService userRestrictionsService;
    @Mock
    private KYCService kycService;
    @Mock
    private UserDecisionContextService userDecisionContextService;
    @Mock
    private NotificationProducer notificationProducer;
    @Mock
    private MessageService messageService;
    @InjectMocks
    private UserServiceFacade userServiceFacade;

//    @Test
//    void testGetAndUpdateUserSession_Success() {
//        // given
//        String deviceId = "deviceId";
//        Lang lang = Lang.EN;
//        UserInfoDto user = getUser(1L);
//        CodeDto codeDto = Instancio.create(CodeDto.class);
//        UserSessionRequestDto request = new UserSessionRequestDto(
//                new ExternalFlags(true),
//                codeDto,
//                new AddressUpdateDto(1, 1, "address")
//        );
//        RestrictionsResponseDto restrictions = Instancio.create(RestrictionsResponseDto.class);
//        UserDecisionContext userDecisionContext = Instancio.create(UserDecisionContext.class);
//
//        when(userService.getUser()).thenReturn(user);
//        when(onboardingService.getOnboardingCheck()).thenReturn(new CheckDto(false, false, null));
//        when(userVerificationService.verify(any())).thenReturn(new VerifyUserResponseDto(true, "message"));
//        when(userRestrictionsService.getUserRestrictions(anyString())).thenReturn(restrictions);
//        when(userDecisionContextService.createOrUpdateUserDecisionContext(anyString(), any(), any()))
//                .thenReturn(userDecisionContext);
//        when(messageService.getMessages(any(), eq(lang))).thenReturn(new HashMap<>());
//
//        // when
//        UserBffResponseDto response = userServiceFacade.getAndUpdateUserSession(deviceId, lang, request);
//
//        // then
//        assertNotNull(response);
//        verify(userService, times(1)).getUser();
//        verify(onboardingService, times(1)).getOnboardingCheck();
//        verify(userRestrictionsService, times(1)).getUserRestrictions(anyString());
//        verify(userDecisionContextService, times(1)).createOrUpdateUserDecisionContext(anyString(), any(), any());
//        assertEquals(user, response.user());
//        assertNotNull(response.metaInfo());
//        assertNotNull(response.notification());
//        assertNotNull(response.additionalCheck());
//    }

//    @Test
//    void updateWarningAction_shouldThrow_whenFeedbackNotCompleted() {
//        WarningActionRequestDto request = new WarningActionRequestDto(ScenarioFeedback.CLOSED);
//
//        BusinessLogicException ex = assertThrows(
//                BusinessLogicException.class,
//                () -> userServiceFacade.updateWarningAction(Lang.UZ, request)
//        );
//
//        assertEquals(ErrorCode.NOT_IMPLEMENTED_YET.getDescription(), ex.getMessage());
//    }

    private UserInfoDto getUser(Long id) {
        return new UserInfoDto(
                "John", "Doe", "123", "<EMAIL>", "1234567890", true,
                UserRole.USER, UserType.CLIENT, UserState.ACTIVE,
                id, "1.0", "", false, 0L, true, true, false, false,
                "apelsinId", 111L, true
        );
    }
}