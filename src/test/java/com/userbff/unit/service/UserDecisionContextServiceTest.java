package com.userbff.unit.service;

import com.userbff.entity.UserDecisionContext;
import com.userbff.enums.ScenarioFeedback;
import com.userbff.enums.ScenarioStatus;
import com.userbff.exception.BusinessLogicException;
import com.userbff.model.restrictions.RestrictionsResponseDto;
import com.userbff.model.user.UserInfoDto;
import com.userbff.model.user.UserState;
import com.userbff.model.user.UserType;
import com.userbff.repository.UserDecisionContextRepository;
import com.userbff.mapper.UserDecisionContextMapper;
import com.userbff.service.UserDecisionContextService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import uz.uzum.commons.starter.data.entity.apelsin.k_app.user.enums.UserRole;

import java.time.LocalDate;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UserDecisionContextServiceTest {
    @Mock
    private UserDecisionContextRepository userDecisionContextRepository;
    @Mock
    private UserDecisionContextMapper userDecisionContextMapper;
    @InjectMocks
    private UserDecisionContextService userDecisionContextService;

//    @Test
//    void createOrUpdateUserDecisionContext_shouldCreateNew_whenNotExists() {
//        RestrictionsResponseDto restrictions = getRestrictions();
//
//        UserDecisionContext mappedContext = new UserDecisionContext();
//        mappedContext.setUserId("1");
//
//        when(userDecisionContextRepository.findByUserId("1")).thenReturn(Optional.empty());
//        when(userDecisionContextMapper.map(any(), eq("1"), eq(restrictions), eq(null))).thenReturn(mappedContext);
//        when(userDecisionContextRepository.save(mappedContext)).thenReturn(mappedContext);
//
//        UserDecisionContext result = userDecisionContextService.createOrUpdateUserDecisionContext("1", restrictions, null);
//
//        assertEquals("1", result.getUserId());
//        verify(userDecisionContextRepository).save(mappedContext);
//    }

//    @Test
//    void updateUserDecisionContextOnFeedbackAction_shouldSetPending_onErrorResponse() {
//        UserInfoDto userInfo = getUserInfoDto();
//        UserDecisionContext existing = new UserDecisionContext();
//        existing.setId(100L);
//
//        when(userDecisionContextRepository.findByUserId("1")).thenReturn(Optional.of(existing));
//        when(userDecisionContextRepository.save(existing)).thenReturn(existing);
//
//        ResponseEntity<RestrictionsResponseDto> errorResponse = ResponseEntity.status(500).build();
//
//        UserDecisionContext result = userDecisionContextService.updateUserDecisionContextOnFeedbackAction(userInfo, errorResponse, ScenarioFeedback.COMPLETED);
//
//        assertEquals(ScenarioStatus.PENDING, result.getScenarioStatus());
//    }

//    @Test
//    void updateUserDecisionContextOnFeedbackAction_shouldSetCompleted_onDifferentExpiryDates() {
//        UserInfoDto userInfo = getUserInfoDto();
//        UserDecisionContext existing = new UserDecisionContext();
//        existing.setId(100L);
//        existing.setExpiryDate(LocalDate.of(2024, 1, 1));
//        existing.setExpiryDatePrev(LocalDate.of(2023, 1, 1));
//
//        when(userDecisionContextRepository.findByUserId("1")).thenReturn(Optional.of(existing));
//        when(userDecisionContextRepository.save(existing)).thenReturn(existing);
//
//        RestrictionsResponseDto restrictions = getRestrictions();
//        ResponseEntity<RestrictionsResponseDto> okResponse = ResponseEntity.ok(restrictions);
//
//        UserDecisionContext result = userDecisionContextService.updateUserDecisionContextOnFeedbackAction(userInfo, okResponse, ScenarioFeedback.COMPLETED);
//
//        assertEquals(ScenarioStatus.COMPLETED, result.getScenarioStatus());
//        assertEquals(ScenarioFeedback.COMPLETED, result.getScenarioFeedback());
//    }

//    @Test
//    void updateUserDecisionContextOnFeedbackAction_shouldThrow_whenNotFound() {
//        UserInfoDto userInfo = getUserInfoDto();
//
//        when(userDecisionContextRepository.findByUserId("1")).thenReturn(Optional.empty());
//
//        assertThrows(BusinessLogicException.class, () ->
//                userDecisionContextService.updateUserDecisionContextOnFeedbackAction(userInfo, ResponseEntity.ok(getRestrictions()), ScenarioFeedback.COMPLETED));
//    }

    private UserInfoDto getUserInfoDto() {
        return new UserInfoDto(
                "John", "Doe", "123", "<EMAIL>", "1234567890",
                true, UserRole.USER, UserType.CLIENT, UserState.ACTIVE,
                1L, "1.0", "", false, 0L, true, true, false, false, "apelsinId",
                111L, true
        );
    }

    private RestrictionsResponseDto getRestrictions() {
        return RestrictionsResponseDto.builder()
                .isResident(true)
                .isActive(true)
                .expiryDate(LocalDate.of(2024, 1, 1))
                .hasRegisteredAddress(true)
                .requiresAddressUpdate(false)
                .customerDataProcessing("isStarted")
                .build();
    }
}