# Spring properties
spring:
  application:
    name: user-bff
  liquibase:
    enabled: true
    change-log: classpath:db/changelog/master-changelog.yml
  kafka:
    consumer:
      group-id: user-restriction-service-test
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        auto-offset-reset: earliest
        allow.auto.create.topics: true
        spring.json.trusted.packages: "*"
        spring.json.type.mapping: "RestrictionsUpdateEvent:com.userbff.model.restrictions.RestrictionsUpdateEvent"
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer

feign:
  client:
    config:
      default:
        connectTimeout: ${FEIGN_DEFAULT_CONNECT_TIMEOUT:5000}
        readTimeout: ${FEIGN_DEFAULT_READ_TIMEOUT:30000}
      banners:
        connect-timeout: ${BANNER_APELSIN_CONTEXT_TIMEOUT:5}
        read-timeout: ${FEIGN_DEFAULT_READ_TIMEOUT:30000}
  pool:
    config:
      default:
        max-total: ${FEIGN_POOL_DEFAULT_MAX_TOTAL:1000}
        max-per-route: ${FEIGN_POOL_DEFAULT_MAX_PER_ROUTE:200}

apelsin:
  url: ${APELSIN_URL:localhost:8082}

user-restrictions:
  url: ${USER_RESTRICTIONS_URL:localhost:8083}
  x-api-key: ${USER_RESTRICTIONS_X_API_KEY:secretik}

user-verification:
  url: ${USER_VERIFICATION_URL:localhost:8084}
  x-api-key: ${USER_VERIFICATION_X_API_KEY:secretik}

kyc:
  url: ${KYC_URL:localhost:8085}
  service-name: ${KYC_SERVICE_NAME:kyc-service}
  x-api-key: ${KYC_API_KEY:secretik}

vault:
  x-api-key: ${X_API_KEY:secretik}