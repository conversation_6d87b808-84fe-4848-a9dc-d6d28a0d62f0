# Web server properties
server:
  port: ${SERVER_PORT:8085}
  shutdown: graceful
  instance: ${SERVER_INSTANCE:2}
  tomcat:
    max-connections: 5000
    max-threads: 5000
# Spring properties
spring:
  application:
    name: user-bff
  jackson:
    serialization:
      indent_output: false
  jpa:
    open-in-view: false
    properties:
      hibernate:
        dialect: "org.hibernate.dialect.PostgreSQLDialect"
    database-platform: "org.hibernate.dialect.PostgreSQLDialect"
  datasource:
    url: ${DB_URL:*****************************************************************}
    username: ${DB_USER:user_bff}
    password: ${DB_PASSWORD:ViVvr5gBnkosCaB6Jfnm}
    driver-class-name: org.postgresql.Driver
  liquibase:
    change-log: classpath:db/changelog/master-changelog.yml
    enabled: true
  kafka:
    bootstrap-servers: ${KAFKA_SASL_BOOTSTRAP_SERVERS:ub-kafka-01.uzumbank-preprod.uzum.io:9092,ub-kafka-02.uzumbank-preprod.uzum.io:9092,ub-kafka-03.uzumbank-preprod.uzum.io:9092}
    security:
      protocol: SASL_SSL
    consumer:
      group-id: ${KAFKA_CONSUMER_GROUP_ID:user_bff_service}
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        allow.auto.create.topics: true
        spring.json.trusted.packages: "*"
        spring.json.type.mapping: "RestrictionsUpdateEvent:com.userbff.model.restrictions.RestrictionsUpdateEvent"
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      properties:
        allow.auto.create.topics: true
        spring.json.trusted.packages: "*"
        spring.json.type.mapping: "RestrictionsUpdateEvent:com.userbff.model.restrictions.RestrictionsUpdateEvent"
    ssl:
      trust-store-certificates: "-----BEGIN CERTIFICATE-----
MIIDBzCCAe+gAwIBAgIUWDcPBVMTr+g8v5yKpqwMC3lC9kUwDQYJKoZIhvcNAQEL
BQAwEzERMA8GA1UEAwwIS2Fma2EtQ0EwHhcNMjUwNDAzMTMyNzUyWhcNMzUwNDAx
MTMyNzUyWjATMREwDwYDVQQDDAhLYWZrYS1DQTCCASIwDQYJKoZIhvcNAQEBBQAD
ggEPADCCAQoCggEBALc2i6uoPVcSdTShO24Hd9Queuh2f+2e70/cOwxYUM6+/O0f
2zuWOVoUAQ54Zx4rmitBjCPvZg1KnvbiewkW6gNBldELPIiM3PGQAptn6fg6zjx3
raFFK4B1lrx0krKHUg15TyBcrcamy0dWRAdbxtFYm9fV6/xDp6VJpkJVYduH1YJV
mJGigQMuhhnd/A5yTNK4jYKVJVTXAUuBbIJyi6op6BmqRU/fxvzYY5lW39WfCDvU
mM7wEVqGaypjfJrjkb0odZ+zjlVXDNfpeTcHBrmH9hvEs3A6UnYHD1oHwhAlyL3S
UPKnXi+9T1dEv/eE3qb9I7oxTtBvuivXndvPdb0CAwEAAaNTMFEwHQYDVR0OBBYE
FK8iIOezRCmpbDA4hTiDUMBQt+4VMB8GA1UdIwQYMBaAFK8iIOezRCmpbDA4hTiD
UMBQt+4VMA8GA1UdEwEB/wQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEBAIzjJrjS
v7c8iEGbvOv4kkAldSGBZpCl1KHk0abs3VZZgpoyIC3Pz67+7byQMYD4KiITsYjp
h9p3yvQaq6xOuDbhIAjF4O6YGlxGerrII47MF9RIi0QOqSbKgziiaRs9lGjUPnNF
7dijUUlwyQHY9c3TGjWHnjYz0JdSFauWyzc6UAgXVXRk4pTqtKuzj6CRjgN++lJL
Ld7Z1CauMpjoz/zolenmGVY0hsh8kI7PptvtQItk3oZ0x1+/Rsgz5zbliNR787b8
ITOiwQis3PBztdSUbJswHJzo0HB3AVKSNxX0Fc9ymEjNFmNZE7OyeqrykYd81ztO
Fbl/lJv8tAQjsKQ=
-----END CERTIFICATE-----"
      trust-store-type: PEM
    properties:
      security.protocol: SASL_SSL
      sasl.mechanism: PLAIN
      sasl.jaas.config: >
        org.apache.kafka.common.security.plain.PlainLoginModule required
        username="svc_preprod_kafka_user_bff_service" password="6GWcDD2LP8iWgJHTLFB460vQACPtiGCM";
      session.timeout.ms: 60000
      metadata.fetch.timeout.ms: 60000
      socket.connection.setup.timeout.ms: 65000
  cloud:
    openfeign:
      client:
        config:
          default:
            connectTimeout: ${FEIGN_DEFAULT_CONNECT_TIMEOUT:5000}
            readTimeout: ${FEIGN_DEFAULT_READ_TIMEOUT:30000}

root:
  level: DEBUG

# Actuator properties
management:
  endpoints:
    web:
      exposure:
        include: "health,info,prometheus"
  metrics:
    tags:
      application: "${spring.application.name}"

sentry:
  dsn: ${SENTRY_DSN:}
  traces-sample-rate: 0.001

apelsin:
  url: ${APELSIN_URL:http://apelsin.svc.uzumbank-preprod.uzum.io}

user-restrictions:
  url: ${USER_RESTRICTIONS_URL:http://user-restriction-service.svc.uzumbank-preprod.uzum.io}
  x-api-key: ${USER_RESTRICTIONS_X_API_KEY:secretik}

user-verification:
  url: ${USER_VERIFICATION_URL:http://user-verification.svc.uzumbank-preprod.uzum.io}
  x-api-key: ${USER_VERIFICATION_X_API_KEY:default-api-key}

kyc:
  url: ${KYC_URL:ub-app-11.uzumbank-preprod.uzum.io:6900}
  service-name: ${KYC_SERVICE_NAME:uzum-bank-mobile}
  x-api-key: ${KYC_API_KEY:5317ab99-be28-4f00-9d90-ba4196d54330}

vault:
  x-api-key: ${X_API_KEY:secretik}

feature-toggle:
  user-verification-from-apelsin: true
  whitelist-enabled: ${FEATURE_WHITELIST_ENABLED:false}

whitelist:
  user-ids:
    - "7127534"
    - "1946555"
    - "17220"
    - "8997719"
    - "6591529"
    - "********"
    - "7055649"
    - "7398882"
    - "6795491"
    - "241395"
    - "8961555"