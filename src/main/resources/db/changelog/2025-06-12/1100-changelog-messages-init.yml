databaseChangeLog:
  - changeSet:
      id: create-messages-table
      author: a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
      changes:
        - createSequence:
            sequenceName: messages_seq
            startValue: 1
            incrementBy: 1

        - createTable:
            tableName: messages
            columns:
              - column:
                  name: id
                  type: BIGINT
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: key
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
              - column:
                  name: lang
                  type: VARCHAR(2)
                  constraints:
                    nullable: false
              - column:
                  name: message
                  type: TEXT
        - addUniqueConstraint:
            tableName: messages
            columnNames: key, lang
            constraintName: unique_message
