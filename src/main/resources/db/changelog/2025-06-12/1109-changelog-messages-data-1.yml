databaseChangeLog:
  - changeSet:
      id: insert-messages-data-1
      author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
      changes:
#          passport_update_resident.title
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_update_resident.title" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Обновите данные паспорта или ID-карты \nдо %date" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_update_resident.title" }
              - column: { name: lang, value: "E<PERSON>" }
              - column: { name: message, value: "Update your passport or ID card \nby %date" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_update_resident.title" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Pasport yoki ID-karta ma'lumotlarini \n%date yangilang" }

#          passport_update_resident.subtitle
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_update_resident.subtitle" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Если документы уже готовы — подтвердите личность, чтобы избежать ограничений на оплаты, переводы и открытие новых продуктов" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_update_resident.subtitle" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "If your documents are ready — verify your identity to avoid restrictions on payments, transfers, and accessing new products" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_update_resident.subtitle" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Hujjatlar tayyor boʻlsa — toʻlov, oʻtkazmalar va yangi mahsulotlarni ochishda cheklovlar boʻlmasligi uchun ma'lumotlarni yangilang" }

#        button.update_data.title
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.update_data.title" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Обновить данные" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.update_data.title" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Update data" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.update_data.title" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Ma'lumotlarni yangilash" }

#        button.later.title
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.later.title" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Позже" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.later.title" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Later" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.later.title" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Keyinroq" }

#          passport_update_nonresident.title
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_update_nonresident.title" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Обновите паспорт \nдо %date" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_update_nonresident.title" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Update your passport \nby %date" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_update_nonresident.title" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Pasportni \n%date yangilang" }

#          passport_update_nonresident.subtitle
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_update_nonresident.subtitle" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Так вы сможете пользоваться всеми продуктами и сервисами банка. Если документ уже готов — посетите пункт выдачи Uzum Market" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_update_nonresident.subtitle" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "This will allow you to use all the bank’s products and services. If the document is ready — visit a Uzum Market pickup point" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_update_nonresident.subtitle" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Shu orqali bankning barcha mahsulotlari va servislaridan foydalanishingiz mumkin. Hujjat tayyor bo‘lsa — Uzum Market topshirish punktiga tashrif buyuring" }

#        button.choose_pickup_point.title
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.choose_pickup_point.title" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Выбрать пункт выдачи" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.choose_pickup_point.title" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Choose pickup point" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.choose_pickup_point.title" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Topshirish punktini tanlash" }

#          passport_expired_resident.title
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_expired_resident.title" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Обновите данные паспорта или ID-карты" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_expired_resident.title" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Update your passport or ID card details" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_expired_resident.title" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Pasport yoki ID karta ma’lumotlarini yangilang" }

#          passport_expired_resident.subtitle
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_expired_resident.subtitle" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "После этого сможете пользоваться всеми продуктами и сервисами банка. Если документ уже готов — обновите данные самостоятельно" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_expired_resident.subtitle" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "After that, you’ll be able to use all the bank’s products and services. If the document is ready — update the information yourself" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_expired_resident.subtitle" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Shundan so‘ng bankning barcha mahsulot va servislaridan foydalanishingiz mumkin. Hujjat tayyor bo‘lsa — ma’lumotlarni mustaqil ravishda yangilang" }

#          passport_expired_nonresident.title
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_expired_nonresident.title" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Обновите паспорт" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_expired_nonresident.title" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Update your passport" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_expired_nonresident.title" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Pasportingizni yangilang" }

#          passport_expired_nonresident.subtitle
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_expired_nonresident.subtitle" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "После этого сможете пользоваться всеми продуктами и сервисами банка. Если документ уже готов — посетите пункт выдачи Uzum Market" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_expired_nonresident.subtitle" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "After that, you’ll be able to use all the bank’s products and services. If the document is ready — visit a Uzum Market pickup point" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "passport_expired_nonresident.subtitle" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Shundan so‘ng bankning barcha mahsulot va servislaridan foydalanishingiz mumkin. Hujjat tayyor bo‘lsa — ma’lumotlarni mustaqil ravishda yangilang" }

#          address_not_found.title
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "address_not_found.title" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Не нашли ваш адрес регистрации" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "address_not_found.title" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "We couldn’t find your registered address" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "address_not_found.title" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Roʻyxatdan oʻtish manzilingiz topilmadi" }

#          address_not_found.subtitle
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "address_not_found.subtitle" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Добавьте его, чтобы подтвердить личность и пользоваться всеми сервисами и услугами банка" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "address_not_found.subtitle" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Add it to verify your identity and access all of the bank’s services" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "address_not_found.subtitle" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Shaxsingizni tasdiqlash va bankning barcha servislari va xizmatlaridan foydalanish uchun manzilni kiriting" }

#        button.add_address.title
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.add_address.title" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Добавить адрес" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.add_address.title" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Add address" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.add_address.title" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Manzilni kiritish" }

#          data_is_under_review.title
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "data_is_under_review.title" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Проверяем данные" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "data_is_under_review.title" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Checking the details" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "data_is_under_review.title" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Ma'lumotlarni tekshirish" }

#          data_is_under_review.subtitle
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "data_is_under_review.subtitle" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Обычно это занимает около суток. Когда всё будет готово, пришлём СМС или пуш-уведомление" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "data_is_under_review.subtitle" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Usually it takes about a day. When everything is ready, we will send you an SMS or push notification" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "data_is_under_review.subtitle" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Odatda bu taxminan bir kun davom etadi. Hammasi tayyor bo'lgach, biz sizga SMS yoki push-bildirishnoma yuboramiz" }

#          address_is_under_review.title
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "address_is_under_review.title" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Проверяем адрес регистрации" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "address_is_under_review.title" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Checking the registration address" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "address_is_under_review.title" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Roʻyxatdan oʻtish manzilini tekshirish" }

#          address_is_under_review.subtitle
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "address_is_under_review.subtitle" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Обычно это занимает пару минут. После проверки сможете пользоваться продуктами и сервисами банка" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "address_is_under_review.subtitle" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Usually it takes a couple of minutes. After verification you will be able to use the bank's products and services" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "address_is_under_review.subtitle" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Odatda bu bir necha daqiqa davom etadi. Tekshiruvdan so'ng siz bank mahsulot va xizmatlaridan foydalanishingiz mumkin bo'ladi" }

#        button.add_address.text
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.add_address.text" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Добавить адрес" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.add_address.text" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Add address" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.add_address.text" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Manzil qo'shing" }

#          data_validation_error.title
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "data_validation_error.title" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Ваши данные не прошли проверку" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "data_validation_error.title" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Your data has not been verified" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "data_validation_error.title" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Maʼlumotlaringiz tasdiqlanmagan" }

#          data_validation_error.subtitle
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "data_validation_error.subtitle" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Напишите в поддержку" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "data_validation_error.subtitle" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Write to support" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "data_validation_error.subtitle" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Qo'llab-quvvatlashga yozing" }

#        button.write_to_support.title
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.write_to_support.title" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Написать в поддержку" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.write_to_support.title" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Write to support" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.write_to_support.title" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Qo'llab-quvvatlashga yozing" }

#          server_error.title
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "server_error.title" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Что-то пошло не так" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "server_error.title" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Something went wrong" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "server_error.title" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Nimadir xato ketdi" }

#          server_error.subtitle
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "server_error.subtitle" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Попробуйте еще раз" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "server_error.subtitle" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Try again" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "server_error.subtitle" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Qayta urinib ko'ring" }

#        button.retry.title
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.retry.title" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Попробовать снова" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.retry.title" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Try again" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.retry.title" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Qayta urinib ko'ring" }

#        button.verify_in_person.title
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.verify_in_person.title" }
              - column: { name: lang, value: "RU" }
              - column: { name: message, value: "Подтвердить личность вживую" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.verify_in_person.title" }
              - column: { name: lang, value: "EN" }
              - column: { name: message, value: "Verify your identity in person" }
        - insert:
            tableName: messages
            columns:
              - column: { name: key, value: "button.verify_in_person.title" }
              - column: { name: lang, value: "UZ" }
              - column: { name: message, value: "Shaxsingizni shaxsan tasdiqlang" }
