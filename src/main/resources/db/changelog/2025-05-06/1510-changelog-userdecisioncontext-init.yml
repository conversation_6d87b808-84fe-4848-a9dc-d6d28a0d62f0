databaseChangeLog:
  - changeSet:
      id: user-decision-context-init
      author: a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
      changes:
        - createSequence:
            sequenceName: user_decision_context_seq
            startValue: 1
            incrementBy: 1

        - createTable:
            tableName: user_decision_context
            columns:
              - column:
                  name: id
                  type: B<PERSON>INT
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: user_id
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
                    unique: true
              - column:
                  name: expiry_date_prev
                  type: DATE
              - column:
                  name: expiry_date
                  type: DATE
              - column:
                  name: scenario_status
                  type: VARCHAR(255)
              - column:
                  name: customer_data_processing
                  type: VARCHAR(255)
              - column:
                  name: scenario_feedback
                  type: VA<PERSON>HAR(255)
              - column:
                  name: scenario_feedback_timestamp
                  type: DATE
              - column:
                  name: scenario_type
                  type: VARCHAR(255)
                  defaultValue: 'NONE'
              - column:
                  name: created_time
                  type: TIMESTAMP

        - createIndex:
            tableName: user_decision_context
            indexName: user_id_index
            unique: true
            columns:
              - column:
                  name: user_id

        # user_flags collection table
        - createTable:
            tableName: user_decision_context_user_flags
            columns:
              - column:
                  name: user_decision_context_id
                  type: BIGINT
                  constraints:
                    nullable: false
              - column:
                  name: user_flags
                  type: VARCHAR(255)
                  constraints:
                    nullable: false

        - addForeignKeyConstraint:
            baseTableName: user_decision_context_user_flags
            baseColumnNames: user_decision_context_id
            referencedTableName: user_decision_context
            referencedColumnNames: id
            constraintName: fk_user_flags_context

        # scenario_flags collection table
        - createTable:
            tableName: user_decision_context_scenario_flags
            columns:
              - column:
                  name: user_decision_context_id
                  type: BIGINT
                  constraints:
                    nullable: false
              - column:
                  name: scenario_flags
                  type: VARCHAR(255)
                  constraints:
                    nullable: false

        - addForeignKeyConstraint:
            baseTableName: user_decision_context_scenario_flags
            baseColumnNames: user_decision_context_id
            referencedTableName: user_decision_context
            referencedColumnNames: id
            constraintName: fk_scenario_flags_context