<?xml version="1.0"?>
<!DOCTYPE suppressions PUBLIC
        "-//Checkstyle//DTD SuppressionFilter Configuration 1.2//EN"
        "https://checkstyle.org/dtds/suppressions_1_2.dtd">

<!-- IMPORTANT ECLIPSE NOTE: If you change this file, you must restart Eclipse
 for your changes to take effect in its Checkstyle integration. -->
<suppressions>
    <!-- Suppress test classes -->
    <suppress files="[/\\]src[/\\].*[Tt]est[/\\](java|groovy)[/\\]" checks="Javadoc*" />
    <suppress files="[/\\]src[/\\].*[Tt]est[/\\](java|groovy)[/\\]" checks="VariableDeclarationUsageDistance" />
    <suppress files="[/\\]src[/\\].*[Tt]est[/\\](java|groovy)[/\\]" checks="VisibilityModifier" />
    <suppress files="[/\\]src[/\\].*[Tt]est[/\\](java|groovy)[/\\]" checks="AvoidStaticImport" />
    <suppress files="[/\\]src[/\\].*[Tt]est[/\\](java|groovy)[/\\]" id="BanSystemOut" />
    <suppress files="[/\\]src[/\\].*[Tt]est[/\\](java|groovy)[/\\]" id="BanSystemErr" />

    <!-- Suppress test resources -->
    <suppress files="[/\\]src[/\\]test[/\\]resources.*" checks="." />

    <!-- JavadocStyle enforces existence of package-info.java package-level Javadoc; we consider this a bug. -->
    <suppress files="package-info.java" checks="JavadocStyle" />

    <!-- non-code classes -->
    <suppress files="\.(bdr|eot|gif|gzip|jar|json|otf|png|svg|ttf|woff|zip)$" checks="FileTabCharacter" />
    <suppress files="\.(bdr|eot|gif|gzip|jar|json|otf|png|svg|ttf|woff|zip)$" checks="NewlineAtEndOfFile" />

    <!-- Generated code should not be subjected to checkstyle. -->
    <suppress files="[/\\].*[/\\]generated.*[/\\]" checks="." />
</suppressions>
