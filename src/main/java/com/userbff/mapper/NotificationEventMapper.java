package com.userbff.mapper;

import com.userbff.enums.EventType;
import com.userbff.enums.Lang;
import com.userbff.model.notification.NotificationEvent;
import com.userbff.model.notification.NotificationEventDto;
import com.userbff.model.notification.button.Button;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface NotificationEventMapper {

    @Mapping(target = "uuid", source = "eventDto.eventId")
    @Mapping(target = "userId", source = "userId")
    @Mapping(target = "eventType", source = "eventType")
    @Mapping(target = "title", source = "eventDto.title")
    @Mapping(target = "subTitle", source = "eventDto.subtitle")
    @Mapping(target = "button", source = "button")
    @Mapping(target = "lang", source = "lang")
    NotificationEvent toNotificationEvent(
            NotificationEventDto eventDto, EventType eventType, Button button, String userId, <PERSON> lang);
}
