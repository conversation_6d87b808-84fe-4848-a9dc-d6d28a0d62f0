package com.userbff.config;

import com.userbff.config.properties.UserVerificationProperties;
import feign.RequestInterceptor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import(FeignCommonConfig.class)
@EnableConfigurationProperties(UserVerificationProperties.class)
public class UserVerificationConfig {

    @Bean("userVerificationInterceptor")
    public RequestInterceptor userVerificationInterceptor(UserVerificationProperties properties) {
        return requestTemplate -> requestTemplate.header("X-API-Key", properties.getXApiKey());
    }
}
