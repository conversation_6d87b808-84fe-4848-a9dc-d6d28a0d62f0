package com.userbff.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import feign.Client;
import feign.Logger;
import feign.RequestInterceptor;
import feign.Retryer;
import feign.codec.Decoder;
import feign.codec.Encoder;
import io.opentelemetry.api.OpenTelemetry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import uz.uzum.logformatter.config.OpenTelemetrySdkComponentsFactory;

import java.util.List;
import java.util.function.Function;

@Configuration
@Slf4j
public class FeignCommonConfig {

    @Bean("feignOpenTelemetryInterceptor")
    public RequestInterceptor feignOpenTelemetryInterceptor(OpenTelemetry openTelemetry) {
        return OpenTelemetrySdkComponentsFactory.createFeignOpentelemetryInterceptor(openTelemetry);
    }

    @Bean
    public Client client(Function<String, Client> feignDefaultClientFactory) {
        return feignDefaultClientFactory.apply("apelsinClient");
    }

    @Bean
    public Encoder feignEncoder() {
        return new SpringEncoder(() -> new HttpMessageConverters(createJacksonConverter(new ObjectMapper()
                .registerModule(new JavaTimeModule()))));
    }

    @Bean
    public Decoder feignDecoder(ObjectMapper objectMapper) {
        MappingJackson2HttpMessageConverter jacksonConverter = createJacksonConverter(objectMapper);

        return new ResponseEntityDecoder(new SpringDecoder(() -> new HttpMessageConverters(jacksonConverter)));
    }

    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    @Bean
    public Retryer retryer() {
        return Retryer.NEVER_RETRY;
    }

    private MappingJackson2HttpMessageConverter createJacksonConverter(ObjectMapper mapper) {
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter(mapper);
        converter.setSupportedMediaTypes(List.of(
                MediaType.APPLICATION_JSON,
                MediaType.APPLICATION_OCTET_STREAM,
                MediaType.TEXT_PLAIN
        ));
        return converter;
    }
}
