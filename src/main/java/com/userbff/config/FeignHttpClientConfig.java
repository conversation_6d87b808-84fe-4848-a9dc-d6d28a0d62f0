package com.userbff.config;

import com.userbff.config.properties.FeignPoolDefaultProperties;
import feign.Client;
import feign.httpclient.ApacheHttpClient;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.httpcomponents.PoolingHttpClientConnectionManagerMetricsBinder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.Registry;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.BasicHttpClientConnectionManager;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.FeignClientProperties;
import org.springframework.cloud.openfeign.FeignClientProperties.FeignClientConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.function.Function;

@Slf4j
@Configuration
@EnableConfigurationProperties(FeignPoolDefaultProperties.class)
@RequiredArgsConstructor
public class FeignHttpClientConfig {

    private final SSLConnectionSocketFactory sslConnectionSocketFactory;
    private final Registry<ConnectionSocketFactory> socketFactoryRegistry;
    private final MeterRegistry meterRegistry;
    private final FeignPoolDefaultProperties defaultPoolProperties;

    @Bean
    public Function<String, Client> feignDefaultClientFactory(FeignClientProperties properties) {
        return (serviceName) -> buildFeignHttpClient(serviceName, properties);
    }

    private Client buildFeignHttpClient(String serviceName, FeignClientProperties properties) {
        CloseableHttpClient httpClient = buildHttpClient(
                serviceName,
                getDefaultConfig(properties).getConnectTimeout(),
                getDefaultConfig(properties).getReadTimeout(),
                defaultPoolProperties.getMaxTotal(),
                defaultPoolProperties.getMaxPerRoute()
        );
        return new ApacheHttpClient(httpClient);
    }

    private CloseableHttpClient buildHttpClient(String serviceName, int connectTimeout, int readTimeout, int maxTotal, int maxPerRoute) {
        return HttpClients.custom()
                .setDefaultRequestConfig(buildRequestConfig(connectTimeout, readTimeout))
                .setConnectionManager(buildConnManager(serviceName, maxTotal, maxPerRoute))
                .setSSLSocketFactory(sslConnectionSocketFactory)
                .setMaxConnTotal(maxTotal)
                .setMaxConnPerRoute(maxPerRoute)
                .build();
    }

    private static RequestConfig buildRequestConfig(int connectTimeout, int readTimeout) {
        return RequestConfig.custom()
                .setConnectTimeout(connectTimeout)
                .setSocketTimeout(readTimeout)
                .setConnectionRequestTimeout(connectTimeout)
                .build();
    }

    private HttpClientConnectionManager buildConnManager(String serviceName, int maxTotal, int maxPerRoute) {
        try {
            PoolingHttpClientConnectionManager connectionManager =
                    new PoolingHttpClientConnectionManager(socketFactoryRegistry);
            connectionManager.setMaxTotal(maxTotal);
            connectionManager.setDefaultMaxPerRoute(maxPerRoute);
            bindPoolMetrics(String.format("feign-%s", serviceName), connectionManager);
            return connectionManager;
        } catch (Exception e) {
            log.error("Error creating pooling http client connection manager for {}", serviceName, e);
            return new BasicHttpClientConnectionManager(socketFactoryRegistry);
        }
    }

    private void bindPoolMetrics(String feignClientName, PoolingHttpClientConnectionManager connectionManager) {
        new PoolingHttpClientConnectionManagerMetricsBinder(connectionManager, feignClientName)
                .bindTo(meterRegistry);
    }

    private static FeignClientConfiguration getDefaultConfig(FeignClientProperties properties) {
        return properties.getConfig().get(properties.getDefaultConfig());
    }
}
