package com.userbff.config.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "whitelist")
public class WhitelistProperties {
    private List<Long> userIds;

    public boolean isWhitelisted(Long userId) {
        return userIds != null && userIds.contains(userId);
    }
}
