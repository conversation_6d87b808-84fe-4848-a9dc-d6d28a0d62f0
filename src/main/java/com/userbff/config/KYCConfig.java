package com.userbff.config;

import com.userbff.config.properties.KYCProperties;
import com.userbff.config.properties.UserVerificationProperties;
import feign.RequestInterceptor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import(FeignCommonConfig.class)
@EnableConfigurationProperties(KYCProperties.class)
public class KYCConfig {

    @Bean("kycInterceptor")
    public RequestInterceptor kycInterceptor(KYCProperties properties) {
        return requestTemplate -> {
            requestTemplate.header("X-BKCP-Client-Service-Name", properties.getServiceName());
            requestTemplate.header("X-BKCP-Client-Service-Api-Key", properties.getXApiKey());
        };
    }
}
