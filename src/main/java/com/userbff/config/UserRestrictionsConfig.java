package com.userbff.config;

import com.userbff.config.properties.UserRestrictionsProperties;
import feign.RequestInterceptor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import(FeignCommonConfig.class)
@EnableConfigurationProperties(UserRestrictionsProperties.class)
public class UserRestrictionsConfig {

    @Bean("userRestrictionsInterceptor")
    public RequestInterceptor userRestrictionsInterceptor(UserRestrictionsProperties properties) {
        return requestTemplate -> requestTemplate.header("X-API-Key", properties.getXApiKey());
    }
}
