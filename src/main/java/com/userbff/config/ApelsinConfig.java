package com.userbff.config;

import com.userbff.interceptor.ApelsinInterceptor;
import feign.RequestInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import(FeignCommonConfig.class)
public class ApelsinConfig {

    @Bean("apelsinInterceptor")
    public RequestInterceptor ApelsinInterceptor() {
        return new ApelsinInterceptor();
    }
}
