package com.userbff.config;

import com.fasterxml.jackson.databind.JsonNode;
import feign.Logger;
import feign.Request;
import feign.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import uz.uzum.logformatter.extractor.ContentExtractor;

import java.io.IOException;
import java.util.Map;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static uz.uzum.logformatter.util.HttpUtils.getContentType;
import static uz.uzum.logformatter.util.LogAttributesUtils.addAttributes;
import static uz.uzum.logformatter.util.LogAttributesUtils.extract;

/**
 * Add class for logger <a
 * href="https://git.uzum.io/bank/backend/lib/log-formatter">log-formatter</a>
 */
@Slf4j
@RequiredArgsConstructor
public class FeignLoggerConfiguration extends Logger {

  private final ContentExtractor contentExtractor;

  @Override
  protected void log(String configKey, String format, Object... args) {
    // necessary
  }

  @Override
  protected Response logAndRebufferResponse(
      String configKey, Level logLevel, Response response, long elapsedTime) {
    return logRequestAndResponse(response);
  }

  private Response logRequestAndResponse(Response response) {
    try {
      Map<String, Object> attributes = extract(response);

      Request request = response.request();
      String requestContentType = getContentType(request.headers()).orElse(APPLICATION_JSON_VALUE);
      JsonNode requestContent =
          contentExtractor.extractContent(request.body(), request.charset(), requestContentType);
      addAttributes(log.atInfo(), attributes, requestContent)
          .log("Outgoing feign request {} {}", request.httpMethod(), request.url());

      int status = response.status();
      if (status != 204 && status != 205) {
        attributes.put("response.status", String.valueOf(status));
        String responseContentType =
            getContentType(response.headers()).orElse(APPLICATION_JSON_VALUE);

        if (response.body() != null) {
          byte[] bodyData = response.body().asInputStream().readAllBytes();

          addAttributes(
                  log.atInfo(),
                  attributes,
                  bodyData,
                  response.charset(),
                  responseContentType,
                  contentExtractor::extractContent)
              .log("Incoming feign response {} {}", request.httpMethod(), request.url());

          return response.toBuilder().body(bodyData).build();
        } else {
          addAttributes(
                  log.atInfo(),
                  attributes,
                  null,
                  response.charset(),
                  responseContentType,
                  contentExtractor::extractContent)
              .log("Incoming feign response {} {}", request.httpMethod(), request.url());
        }
      }

      return response;
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return response;
    }
  }
}
