package com.userbff.filter;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.filter.OncePerRequestFilter;

import java.util.Set;

public abstract class BaseFilter extends OncePerRequestFilter {
    public static final Set<String> UNSECURED_PATHS = Set.of(
            "/swagger",
            "/api-docs",
            "/csrf",
            "/actuator",
            "/hystrix.stream",
            "/hystrix",
            "/health",
            "/ready",
            "/live"
    );

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String path = request.getRequestURI();
        return UNSECURED_PATHS.stream().anyMatch(path::contains);
    }
}
