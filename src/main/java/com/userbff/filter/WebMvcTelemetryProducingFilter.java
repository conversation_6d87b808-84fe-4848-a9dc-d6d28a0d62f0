package com.userbff.filter;

import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.context.Context;
import io.opentelemetry.context.Scope;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import uz.uzum.logformatter.otel.instrumentation.upstream.impl.JakartaInstrumenter;
import uz.uzum.logformatter.otel.textmap.getter.JakartaHttpServletRequestGetter;

import java.io.IOException;

@Component
public class WebMvcTelemetryProducingFilter extends BaseFilter {
    private final JakartaInstrumenter instrumenter;

    public WebMvcTelemetryProducingFilter(OpenTelemetry openTelemetry) {
        this.instrumenter = new JakartaInstrumenter(openTelemetry, JakartaHttpServletRequestGetter.INSTANCE);
    }

    @Override
    public void doFilterInternal(
            HttpServletRequest request,
            HttpServletResponse response,
            FilterChain filterChain
    ) throws ServletException, IOException {
        Context context = instrumenter.instrument(request);
        try (Scope ignored = context.makeCurrent()) {
            filterChain.doFilter(request, response);
        } finally {
            Span.fromContext(context).end();
        }
    }
}
