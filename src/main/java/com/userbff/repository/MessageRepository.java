package com.userbff.repository;

import com.userbff.entity.Message;
import com.userbff.enums.Lang;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface MessageRepository extends JpaRepository<Message, Long> {
    Optional<Message> findByKeyAndLang(String key, Lang lang);

    List<Message> findAllByKeyInAndLang(List<String> keys, Lang lang);
}
