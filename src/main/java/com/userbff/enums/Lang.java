package com.userbff.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Locale;

@Getter
@RequiredArgsConstructor
public enum Lang {
    UZ("UZ"),
    RU("RU"),
    EN("EN"),
    FR("FR"),
    DE("DE"),
    TR("TR"),
    TG("TG"),
    KAA("KAA");

    private final String name;

    public static Lang getByName(final String name) {
        for (Lang lang : Lang.values()) {
            if (lang.getName().equals(name)) {
                return lang;
            }
        }
        return EN;
    }

    public Locale toLocale() {
        return new Locale(name.toLowerCase(), "UZ");
    }
}
