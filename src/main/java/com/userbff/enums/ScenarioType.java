package com.userbff.enums;

import com.userbff.model.notification.button.ActionType;
import com.userbff.model.notification.button.Button;
import com.userbff.model.notification.button.ButtonAction;
import com.userbff.model.notification.button.ButtonActionType;
import com.userbff.model.notification.button.ButtonType;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Getter
public enum ScenarioType {
    PASSPORT_UPDATE_RESIDENT(
            "passport_update_resident.title",
            "passport_update_resident.subtitle",
            List.of(
                    new Button(
                            "button.update_data.title",
                            ScenarioFeedback.OPENED,
                            ButtonType.PRIMARY,
                            new ButtonAction(
                                    ButtonActionType.DEEPLINK,
                                    "https://apelsin.uz/goto?action=myid_identification"
                            )
                    ),
                    new Button(
                            "button.later.title",
                            ScenarioFeedback.CLOSED,
                            ButtonType.SECONDARY,
                            new ButtonAction(
                                    ButtonActionType.ACTION,
                                    ActionType.CLOSE.name()
                            )
                    )
            ),
            "id"
    ),
    PASSPORT_UPDATE_NONRESIDENT(
            "passport_update_nonresident.title",
            "passport_update_nonresident.subtitle",
            List.of(
                    new Button(
                            "button.choose_pickup_point.title",
                            ScenarioFeedback.OPENED,
                            ButtonType.PRIMARY,
                            new ButtonAction(
                                    ButtonActionType.DEEPLINK,
                                    "https://apelsin.uz/goto?action=pvz_identification"
                            )
                    ),
                    new Button(
                            "button.later.title",
                            ScenarioFeedback.CLOSED,
                            ButtonType.SECONDARY,
                            new ButtonAction(
                                    ButtonActionType.ACTION,
                                    ActionType.CLOSE.name()
                            )
                    )
            ),
            "id"
    ),
    PASSPORT_EXPIRED_RESIDENT(
            "passport_expired_resident.title",
            "passport_expired_resident.subtitle",
            List.of(
                    new Button(
                            "button.update_data.title",
                            ScenarioFeedback.OPENED,
                            ButtonType.PRIMARY,
                            new ButtonAction(
                                    ButtonActionType.DEEPLINK,
                                    "https://apelsin.uz/goto?action=myid_identification"
                            )
                    )
            ),
            "id"
    ),
    PASSPORT_EXPIRED_NONRESIDENT(
            "passport_expired_nonresident.title",
            "passport_expired_nonresident.subtitle",
            List.of(
                    new Button(
                            "button.choose_pickup_point.title",
                            ScenarioFeedback.OPENED,
                            ButtonType.PRIMARY,
                            new ButtonAction(
                                    ButtonActionType.DEEPLINK,
                                    "https://apelsin.uz/goto?action=pvz_identification"
                            )
                    )
            ),
            "id"
    ),
    ADDRESS_UPDATE(
            "address_not_found.title",
            "address_not_found.subtitle",
            List.of(
                    new Button(
                            "button.add_address.title",
                            ScenarioFeedback.OPENED,
                            ButtonType.PRIMARY,
                            new ButtonAction(
                                    ButtonActionType.DEEPLINK,
                                    "https://apelsin.uz/goto?action=registration_address"
                            )
                    )
            ),
            "pin-map"
    ),
    DATA_IS_UNDER_REVIEW(
            "data_is_under_review.title",
            "data_is_under_review.subtitle",
            List.of(),
            "clock"
    ),
    ADDRESS_IS_UNDER_REVIEW(
            "address_is_under_review.title",
            "address_is_under_review.subtitle",
            List.of(),
            "clock"
    ),
    DATA_VALIDATION_ERROR(
            "data_validation_error.title",
            "data_validation_error.subtitle",
            List.of(
                    new Button(
                            "button.write_to_support.title",
                            ScenarioFeedback.OPENED,
                            ButtonType.PRIMARY,
                            new ButtonAction(
                                    ButtonActionType.DEEPLINK,
                                    "https://apelsin.uz/goto?action=chat"
                            )
                    )
            ),
            "support"
    ),
    ONBOARDING_FAIL(
            "server_error.title",
            "server_error.subtitle",
            List.of(
                    new Button(
                            "button.retry.title",
                            ScenarioFeedback.OPENED,
                            ButtonType.PRIMARY,
                            new ButtonAction(
                                    ButtonActionType.ACTION,
                                    ActionType.UPDATE.name()
                            )
                    ),
                    new Button(
                            "button.verify_in_person.title",
                            ScenarioFeedback.OPENED,
                            ButtonType.SECONDARY,
                            new ButtonAction(
                                    ButtonActionType.DEEPLINK,
                                    "https://apelsin.uz/goto?action=pvz_identification"
                            )
                    )
            ),
            "retry"
    ),
    NONE(null, null, List.of(), null);

    private final String title;
    private final String subTitle;
    private final List<Button> buttons;
    private final String icon;

    public List<String> getMessageKeys() {
        List<String> messageKeys = new ArrayList<>();
        messageKeys.add(title);
        messageKeys.add(subTitle);
        messageKeys.addAll(
                buttons.stream()
                        .map(Button::title)
                        .toList()
        );
        messageKeys.addAll(
                buttons.stream()
                        .map(Button::action)
                        .filter(buttonAction -> buttonAction.type() == ButtonActionType.ACTION)
                        .map(ButtonAction::payload)
                        .toList()
        );
        return messageKeys;
    }
}
