package com.userbff.interceptor;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.List;
import java.util.Optional;

import static com.userbff.enums.HeaderConstants.AUTHORIZATION;
import static com.userbff.enums.HeaderConstants.DEVICE_ID;

/**
 * Apelsin feign interceptor
 */
@RequiredArgsConstructor
public class ApelsinInterceptor implements RequestInterceptor {
    private static final List<String> HEADERS_TO_RETHROW = List.of(AUTHORIZATION, DEVICE_ID);

    @Override
    public void apply(RequestTemplate requestTemplate) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            HEADERS_TO_RETHROW.forEach(headerName -> Optional.ofNullable(request.getHeader(headerName))
                    .ifPresent(header -> requestTemplate.header(headerName, header)));
        }
    }
}