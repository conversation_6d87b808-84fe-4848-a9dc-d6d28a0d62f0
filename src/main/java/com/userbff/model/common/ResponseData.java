package com.userbff.model.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@JsonInclude(value = JsonInclude.Include.NON_NULL)
public record ResponseData<T>(
        @JsonProperty("data") T data,
        @JsonProperty("errorMessage") String errorMessage,
        @JsonProperty("timestamp") long timestamp,
        String errorCodeString
) {
    public ResponseData(T data) {
        this(data, "", System.currentTimeMillis(), null);
    }

    public ResponseData(String successMessage) {
        this((T) successMessage, "", System.currentTimeMillis(), null);
    }

    public ResponseData(T data, String errorMessage) {
        this(data, errorMessage, System.currentTimeMillis(), null);
    }

    public ResponseData(String errorCodeString, String errorMessage) {
        this(null, errorMessage, System.currentTimeMillis(), errorCodeString);
    }

    public ResponseData() {
        this(null, "", System.currentTimeMillis(), null);
    }

    public static <T> ResponseEntity<ResponseData<T>> response(T data) {
        return ResponseEntity.ok(new ResponseData<>(data));
    }

    public static <T> ResponseEntity<ResponseData<T>> success() {
        return ResponseEntity.ok(new ResponseData<>("SUCCESS"));
    }

    public static <T> ResponseEntity<ResponseData<T>> response(ResponseData<T> responseData, HttpStatus status) {
        return new ResponseEntity<>(responseData, status);
    }

    public static <T> ResponseEntity<ResponseData<T>> response(T data, HttpStatus httpStatus) {
        return new ResponseEntity<>(new ResponseData<>(data), httpStatus);
    }

    public static <T> ResponseEntity<ResponseData<T>> response(String errorMessage, HttpStatus httpStatus) {
        return new ResponseEntity<>(new ResponseData<>(null, errorMessage), httpStatus);
    }

    public static <T> ResponseEntity<ResponseData<T>> responseBadRequest(String errorMessage) {
        return new ResponseEntity<>(new ResponseData<>(null, errorMessage), HttpStatus.BAD_REQUEST);
    }
}
