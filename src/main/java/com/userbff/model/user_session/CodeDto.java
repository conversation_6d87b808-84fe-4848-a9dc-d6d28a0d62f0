package com.userbff.model.user_session;


import com.userbff.enums.BankType;

import java.io.Serializable;
import java.util.List;

public record CodeDto(

        String code,

        BankType bankType,

        String token,

        Float comparisonValue,

        List<DocumentUrl> documentUrls,

        String externalId,

        String imageUrl,

        Boolean isVerificationOnlyRequired
) implements Serializable {
}
