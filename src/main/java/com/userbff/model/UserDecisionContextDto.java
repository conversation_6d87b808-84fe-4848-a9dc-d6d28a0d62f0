package com.userbff.model;

import com.userbff.enums.ScenarioFeedback;
import com.userbff.enums.ScenarioFlag;
import com.userbff.enums.ScenarioStatus;
import com.userbff.enums.ScenarioType;
import com.userbff.enums.UserFlag;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Data
@NoArgsConstructor
public class UserDecisionContextDto {
    private Long id;
    private String userId;
    private LocalDate expiryDatePrev;
    private LocalDate expiryDate;
    private ScenarioStatus scenarioStatus;
    private String customerDataProcessing;
    private ScenarioFeedback scenarioFeedback;
    private LocalDate scenarioFeedbackTimestamp;
    private ScenarioType scenarioType;
    private Set<UserFlag> userFlags = new HashSet<>();
    private Set<ScenarioFlag> scenarioFlags = new HashSet<>();
    private LocalDateTime createdTime;
    private boolean isClosable;
}
