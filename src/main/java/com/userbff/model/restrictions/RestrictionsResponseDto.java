package com.userbff.model.restrictions;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

import java.time.LocalDate;

@Builder
public record RestrictionsResponseDto(
    @JsonProperty("isResident")
    Boolean isResident,
    @JsonProperty("isActive")
    Boolean isActive,
    @JsonProperty("expiryDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    LocalDate expiryDate,
    @JsonProperty("hasRegisteredAddress")
    Boolean hasRegisteredAddress,
    @JsonProperty("requiresAddressUpdate")
    Boolean requiresAddressUpdate,
    @JsonProperty("customerDataProcessing")
    String customerDataProcessing,
    @JsonProperty("resolution")
    String resolution
) {
}