package com.userbff.model.user;

import uz.uzum.commons.starter.data.entity.apelsin.k_app.user.enums.UserRole;

import java.io.Serial;
import java.io.Serializable;

public record UserInfoDto(
        String firstName,
        String lastName,
        String customerId,
        String email,
        String phoneNumber,
        Boolean notification,
        UserRole userRole,
        UserType userType,
        UserState userState,
        Long userId,
        String appVersion,
        String profileUrl,
        Boolean hasRated,
        Long registeredDate,
        Boolean isEmailConfirmed,
        Boolean hasPinfl,
        Boolean isNewUser,
        Boolean isSleeping,
        String apelsinCustomerId,
        Long identificationId,
        Boolean isClient
) implements Serializable {
    @Serial
    private static final long serialVersionUID = -2291422797686981064L;
}
