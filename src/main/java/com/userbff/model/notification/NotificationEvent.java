package com.userbff.model.notification;

import com.userbff.enums.EventType;
import com.userbff.enums.Lang;
import com.userbff.model.notification.button.ButtonAction;
import java.util.UUID;
import lombok.Builder;

@Builder
public record NotificationEvent(
        UUID uuid, String userId, EventType eventType, String title, String subTitle, Button button, Lang lang) {

    public record Button(String title, ButtonAction action) {}
}
