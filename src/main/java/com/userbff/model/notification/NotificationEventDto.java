package com.userbff.model.notification;

import com.userbff.enums.ScenarioStatus;
import com.userbff.enums.ScenarioType;
import com.userbff.model.notification.button.Button;

import java.util.List;
import java.util.UUID;

public record NotificationEventDto(
        Long userDecisionContextId,
        UUID eventId,
        ScenarioType type,
        String title,
        String subtitle,
        List<Button> buttons,
        String icon,
        ScenarioStatus status,
        boolean closable,
        boolean showOnStart
) {
}
