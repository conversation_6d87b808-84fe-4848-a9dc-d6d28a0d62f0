package com.userbff.controller;

import com.userbff.config.ApiVersion;
import com.userbff.enums.Lang;
import com.userbff.model.NotificationResponseDto;
import com.userbff.model.UserBffResponseDto;
import com.userbff.model.kyc.regions.RegionsDistrictResponseDto;
import com.userbff.model.notification.NotificationEventDto;
import com.userbff.model.user_session.UserSessionRequestDto;
import com.userbff.model.user_session.WarningActionRequestDto;
import com.userbff.service.UserServiceFacade;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.userbff.enums.HeaderConstants.API_VERSION;
import static com.userbff.enums.HeaderConstants.DEVICE_ID;
import static com.userbff.enums.HeaderConstants.USER_LANG;

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/api/user-bff", produces = MediaType.APPLICATION_JSON_VALUE, headers = API_VERSION + "=" + ApiVersion.V1)
public class UserBffController {
    private final UserServiceFacade userServiceFacade;

    @PostMapping(value = "/user-session")
    public ResponseEntity<UserBffResponseDto> getAndUpdateUserSession(
            @RequestHeader(DEVICE_ID) String deviceId,
            @RequestHeader(value = USER_LANG, defaultValue = "UZ") String userLang,
            @RequestBody UserSessionRequestDto requestDto
    ) {
        UserBffResponseDto response = userServiceFacade.getAndUpdateUserSession(deviceId, Lang.getByName(userLang), requestDto);

        return ResponseEntity.ok(response);
    }

    @PostMapping(value = "/document-warning-action")
    public ResponseEntity<NotificationResponseDto> updateWarningAction(
            @RequestHeader(value = USER_LANG, defaultValue = "UZ") String userLang,
            @RequestBody WarningActionRequestDto requestDto
    ) {
        NotificationEventDto response = userServiceFacade.updateWarningAction(Lang.getByName(userLang), requestDto);

        return ResponseEntity.ok(new NotificationResponseDto(response));
    }

//    @GetMapping(value = "/dictionaries/regions-districts")
//    public ResponseEntity<RegionsDistrictResponseDto> getRegionsDistrictDictionary() {
//        return ResponseEntity.ok(userServiceFacade.getRegionsDistrictDictionary());
//    }
}
