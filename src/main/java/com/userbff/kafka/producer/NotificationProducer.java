package com.userbff.kafka.producer;

import static com.userbff.config.KafkaConfig.NOTIFICATIONS_UPDATE_TOPIC;
import static com.userbff.enums.RequestStatus.FAILURE;
import static com.userbff.enums.RequestStatus.REQUEST;
import static com.userbff.enums.RequestStatus.SUCCESS;
import static com.userbff.util.MetricsConstants.ERROR_INFO_TAG;
import static com.userbff.util.MetricsConstants.KAFKA_EVENT;
import static com.userbff.util.MetricsConstants.KAFKA_EVENT_TIMER;
import static com.userbff.util.MetricsConstants.KAFKA_STREAM_TYPE_TAG;
import static com.userbff.util.MetricsConstants.PRODUCER;
import static com.userbff.util.MetricsConstants.STATUS_TAG;
import static com.userbff.util.MetricsConstants.TOPIC_TAG;

import com.userbff.config.KafkaConfig;
import com.userbff.model.notification.NotificationEvent;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.sentry.Sentry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import uz.uzum.kafka.v3.producer.UzumKafkaProducer;

@Slf4j
@Component
@RequiredArgsConstructor
public class NotificationProducer {

    private final MeterRegistry meterRegistry;
    private final UzumKafkaProducer<NotificationEvent> kafkaProducer;

    public void sendNotificationEvent(NotificationEvent message, String messageId) {
        Timer.Sample sample = Timer.start(meterRegistry);
        try {
            log.info("Send document update event: {} to queue: {}", message, NOTIFICATIONS_UPDATE_TOPIC);
            meterRegistry
                    .counter(
                            KAFKA_EVENT,
                            KAFKA_STREAM_TYPE_TAG,
                            PRODUCER,
                            TOPIC_TAG,
                            NOTIFICATIONS_UPDATE_TOPIC,
                            STATUS_TAG,
                            REQUEST.getStatus())
                    .increment();

            kafkaProducer
                    .message(message)
                    .withTopic(KafkaConfig.NOTIFICATIONS_UPDATE_TOPIC)
                    .withKey(messageId)
                    .send();

            meterRegistry
                    .counter(
                            KAFKA_EVENT,
                            KAFKA_STREAM_TYPE_TAG,
                            PRODUCER,
                            TOPIC_TAG,
                            NOTIFICATIONS_UPDATE_TOPIC,
                            STATUS_TAG,
                            SUCCESS.getStatus())
                    .increment();
            log.info("Forwarded message: {} to queue: {}", message, NOTIFICATIONS_UPDATE_TOPIC);
        } catch (Exception e) {
            Sentry.captureException(e);
            log.error("Error sending notification", e);
            meterRegistry
                    .counter(
                            KAFKA_EVENT,
                            KAFKA_STREAM_TYPE_TAG,
                            PRODUCER,
                            TOPIC_TAG,
                            NOTIFICATIONS_UPDATE_TOPIC,
                            STATUS_TAG,
                            FAILURE.getStatus(),
                            ERROR_INFO_TAG,
                            e.getMessage())
                    .increment();
        } finally {
            sample.stop(meterRegistry.timer(
                    KAFKA_EVENT_TIMER, KAFKA_STREAM_TYPE_TAG, PRODUCER, TOPIC_TAG, NOTIFICATIONS_UPDATE_TOPIC));
        }
    }
}
