package com.userbff.service.message;

import com.userbff.entity.Message;
import com.userbff.enums.Lang;
import com.userbff.repository.MessageRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MessageService {
    private final MessageRepository messageRepository;

    public String getMessage(String key) {
        return getMessage(key, Lang.UZ);
    }

    public String getMessage(String key, Lang lang) {
        return messageRepository.findByKeyAndLang(key, lang)
                .map(Message::getMessage)
                .orElse(key);
    }

    public Map<String, String> getMessages(List<String> keys, Lang lang) {
        return messageRepository.findAllByKeyInAndLang(keys, lang).stream()
                .collect(Collectors.toMap(Message::getKey, Message::getMessage));
    }

    public Map<String, String> getMessagesAndReplaceDate(List<String> keys, Lang lang, LocalDate date) {
        Map<String, String> messages = messageRepository.findAllByKeyInAndLang(keys, lang).stream()
                .collect(Collectors.toMap(Message::getKey, Message::getMessage));
        return messages.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            Optional<String> formattedDateOpt = Optional.ofNullable(date)
                                    .map(expiryDate -> expiryDate.getDayOfMonth() + " " +
                                            expiryDate.getMonth().getDisplayName(java.time.format.TextStyle.FULL, new Locale(lang.name())));
                            return entry.getValue().contains("%date") && formattedDateOpt.isPresent()
                                    ? entry.getValue().replace("%date", formattedDateOpt.get())
                                    : entry.getValue();
                        }
                ));
    }
}