package com.userbff.service.restrictions;

import com.userbff.client.UserRestrictionsClient;
import com.userbff.exception.FeignClientException;
import com.userbff.exception.ServiceUnavailableException;
import com.userbff.model.restrictions.RestrictionsResponseDto;
import feign.FeignException;
import feign.RetryableException;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.sentry.Sentry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import static com.userbff.enums.HttpMethodType.GET;
import static com.userbff.enums.RequestStatus.REQUEST;
import static com.userbff.enums.RequestStatus.SUCCESS;
import static com.userbff.util.MetricsConstants.DESTINATION_TAG;
import static com.userbff.util.MetricsConstants.FEIGN_REQUEST;
import static com.userbff.util.MetricsConstants.FEIGN_REQUEST_TIMER;
import static com.userbff.util.MetricsConstants.HTTP_REQUEST_TYPE_TAG;
import static com.userbff.util.MetricsConstants.HTTP_REQUEST_URL_TAG;
import static com.userbff.util.MetricsConstants.STATUS_TAG;
import static com.userbff.util.MetricsConstants.USER_RESTRICTIONS_SERVICE;

@Slf4j
@Service
public class UserRestrictionsService {
    private static final String USER_RESTRICTIONS_URL = "/user-restrictions/{userId}";
    private final UserRestrictionsClient userRestrictionsClient;
    private final MeterRegistry meterRegistry;
    private final Counter userRestrictionsRequestCounter;
    private final Counter userRestrictionsSuccessCounter;
    private final Counter userRestrictionsFailureCounter;
    private final Timer userRestrictionsTimer;

    public UserRestrictionsService(
            UserRestrictionsClient userRestrictionsClient,
            MeterRegistry meterRegistry
    ) {
        this.userRestrictionsClient = userRestrictionsClient;
        this.meterRegistry = meterRegistry;

        userRestrictionsRequestCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, USER_RESTRICTIONS_SERVICE,
                HTTP_REQUEST_TYPE_TAG, GET.name(),
                HTTP_REQUEST_URL_TAG, USER_RESTRICTIONS_URL,
                STATUS_TAG, REQUEST.getStatus()
        );
        userRestrictionsSuccessCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, USER_RESTRICTIONS_SERVICE,
                HTTP_REQUEST_TYPE_TAG, GET.name(),
                HTTP_REQUEST_URL_TAG, USER_RESTRICTIONS_URL,
                STATUS_TAG, SUCCESS.getStatus()
        );
        userRestrictionsFailureCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, USER_RESTRICTIONS_SERVICE,
                HTTP_REQUEST_TYPE_TAG, GET.name(),
                HTTP_REQUEST_URL_TAG, USER_RESTRICTIONS_URL,
                STATUS_TAG, REQUEST.getStatus()
        );
        userRestrictionsTimer = meterRegistry.timer(
                FEIGN_REQUEST_TIMER,
                DESTINATION_TAG, USER_RESTRICTIONS_SERVICE,
                HTTP_REQUEST_TYPE_TAG, GET.name(),
                HTTP_REQUEST_URL_TAG, USER_RESTRICTIONS_URL
        );
    }

    public RestrictionsResponseDto getUserRestrictions(String userId) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            userRestrictionsRequestCounter.increment();
            log.info("invoke UserRestrictionsService.getUserRestrictions() for userId: %s".formatted(userId));

            ResponseEntity<RestrictionsResponseDto> userRestrictionsRaw = userRestrictionsClient.getUserRestrictions(userId);
            userRestrictionsSuccessCounter.increment();

            if (!userRestrictionsRaw.getStatusCode().is2xxSuccessful()) {
                throw new ServiceUnavailableException("User-Restrictions Service is temporarily unavailable, please try again later");
            }

            return userRestrictionsRaw.getBody();
        } catch (RetryableException e) {
            userRestrictionsFailureCounter.increment();
            log.error("User-Restrictions Service is temporarily unavailable, please try again later", e);
            throw new ServiceUnavailableException("User-Restrictions Service is temporarily unavailable, please try again later", e);
        } catch (FeignException e) {
            userRestrictionsFailureCounter.increment();
            log.error("An error occurred while getting user restrictions: {}", e.contentUTF8(), e);
            throw new FeignClientException("An error occurred while getting user restrictions: " + e.contentUTF8(), e);
        } catch (Exception e) {
            Sentry.captureException(e);
            userRestrictionsFailureCounter.increment();
            log.error("An error occurred while getting user restrictions: " + e.getMessage(), e);
            throw e;
        } finally {
            sample.stop(userRestrictionsTimer);
        }
    }
}
