package com.userbff.service;

import com.userbff.config.properties.WhitelistProperties;
import com.userbff.enums.EventType;
import com.userbff.enums.Lang;
import com.userbff.enums.ScenarioFlag;
import com.userbff.enums.ScenarioType;
import com.userbff.kafka.producer.NotificationProducer;
import com.userbff.mapper.NotificationEventMapper;
import com.userbff.model.UserBffResponseDto;
import com.userbff.model.UserDecisionContextDto;
import com.userbff.model.notification.NotificationEvent;
import com.userbff.model.notification.NotificationEventDto;
import com.userbff.model.notification.button.Button;
import com.userbff.model.notification.button.ButtonAction;
import com.userbff.model.notification.button.ButtonType;
import com.userbff.model.onboarding.CheckDto;
import com.userbff.model.restrictions.RestrictionsResponseDto;
import com.userbff.model.restrictions.RestrictionsUpdateEvent;
import com.userbff.model.user.AdditionalCheckDto;
import com.userbff.model.user.UserInfoDto;
import com.userbff.model.user.UserMetaDto;
import com.userbff.model.user_session.ExternalFlags;
import com.userbff.model.user_session.UserSessionRequestDto;
import com.userbff.model.user_session.WarningActionRequestDto;
import com.userbff.model.verification.VerifyUserRequestDto;
import com.userbff.model.verification.VerifyUserResponseDto;
import com.userbff.service.kyc.KYCService;
import com.userbff.service.message.MessageService;
import com.userbff.service.onboarding.OnboardingService;
import com.userbff.service.restrictions.UserRestrictionsService;
import com.userbff.service.user.UserService;
import com.userbff.service.verification.UserVerificationService;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class UserServiceFacade {

    private final UserService userService;
    private final UserVerificationService userVerificationService;
    private final OnboardingService onboardingService;
    private final UserRestrictionsService userRestrictionsService;
    private final KYCService kycService;
    private final UserDecisionContextService userDecisionContextService;
    private final NotificationProducer notificationProducer;
    private final MessageService messageService;
    private final WhitelistProperties whitelistProperties;
    @Value("${feature-toggle.whitelist-enabled}")
    private Boolean isWhitelistEnabled;
    private final NotificationEventMapper notificationEventMapper;

    public UserBffResponseDto getAndUpdateUserSession(String deviceId, Lang lang, UserSessionRequestDto requestDto) {
        UserInfoDto user = userService.getUser();

        Boolean isUpdateUserActivityNeed = Optional.ofNullable(requestDto.externalFlags())
                .map(ExternalFlags::isUpdateUserActivityNeed)
                .orElse(false);
        if (isUpdateUserActivityNeed) {
            userService.updateUserActivity();
        }

        CheckDto onboardingResponse =
                requestDto.checkDto() == null ? onboardingService.getOnboardingCheck() : requestDto.checkDto();

        boolean verificationStatus =
                userVerificationService.getVerificationStatus(user.userId().toString(), deviceId);
        if (requestDto.myId() != null && !verificationStatus) {

            VerifyUserResponseDto verifyUserResponseDto = userVerificationService.verify(new VerifyUserRequestDto(
                    user.userId().toString(), deviceId, requestDto.myId().externalId()));
            verificationStatus = verifyUserResponseDto.success();
        }

        if (isWhitelistEnabled && !whitelistProperties.isWhitelisted(user.userId())) {
            return new UserBffResponseDto(
                    user,
                    new UserMetaDto(
                            verificationStatus,
                            onboardingResponse.identificationState(),
                            true,
                            !onboardingResponse.hasUzumCard(),
                            null),
                    null,
                    new AdditionalCheckDto("MY_ID"));
        }

        RestrictionsResponseDto restrictions =
                userRestrictionsService.getUserRestrictions(user.userId().toString());

        UserDecisionContextDto userDecisionContext = userDecisionContextService.createOrUpdateUserDecisionContext(
                user.userId().toString(), restrictions, onboardingResponse);

        if (userDecisionContext == null) {
            return new UserBffResponseDto(
                    user,
                    new UserMetaDto(
                            verificationStatus,
                            onboardingResponse.identificationState(),
                            restrictions == null
                                    || restrictions.expiryDate() == null
                                    || restrictions.expiryDate().isAfter(LocalDate.now()),
                            !onboardingResponse.hasUzumCard(),
                            Optional.ofNullable(restrictions)
                                    .map(RestrictionsResponseDto::isResident)
                                    .orElse(null)),
                    null,
                    new AdditionalCheckDto("MY_ID"));
        }

        ScenarioType scenarioType = userDecisionContext.getScenarioType();
        Map<String, String> messages = messageService.getMessagesAndReplaceDate(
                scenarioType.getMessageKeys(), lang, userDecisionContext.getExpiryDate());

        return new UserBffResponseDto(
                user,
                new UserMetaDto(
                        verificationStatus,
                        onboardingResponse.identificationState(),
                        restrictions == null
                                || restrictions.expiryDate() == null
                                || restrictions.expiryDate().isAfter(LocalDate.now()),
                        !onboardingResponse.hasUzumCard(),
                        Optional.ofNullable(restrictions)
                                .map(RestrictionsResponseDto::isResident)
                                .orElse(null)),
                createNotificationEventDto(scenarioType, userDecisionContext, onboardingResponse, messages),
                new AdditionalCheckDto("MY_ID"));
    }

    public NotificationEventDto updateWarningAction(Lang lang, WarningActionRequestDto warningAction) {
        UserDecisionContextDto userDecisionContext =
                userDecisionContextService.updateUserDecisionContextOnFeedbackAction(warningAction);

        ScenarioType scenarioType = userDecisionContext.getScenarioType();
        Map<String, String> messages = messageService.getMessagesAndReplaceDate(
                scenarioType.getMessageKeys(), lang, userDecisionContext.getExpiryDate());

        return createNotificationEventDto(scenarioType, userDecisionContext, null, messages);
    }

    //    public RegionsDistrictResponseDto getRegionsDistrictDictionary() {
    //        return kycService.getRegionsDistrictDictionary();
    //    }

    private NotificationEventDto createNotificationEventDto(
            ScenarioType scenarioType,
            UserDecisionContextDto userDecisionContext,
            @Nullable CheckDto onboardingResponse,
            Map<String, String> messages) {
        if (scenarioType == ScenarioType.NONE) {
            return null;
        }
        boolean hasUzumCard = Optional.ofNullable(onboardingResponse)
                .map(CheckDto::hasUzumCard)
                .orElse(false);
        return new NotificationEventDto(
                userDecisionContext.getId(),
                UUID.randomUUID(),
                scenarioType,
                messages.getOrDefault(scenarioType.getTitle(), scenarioType.getTitle()),
                messages.getOrDefault(scenarioType.getSubTitle(), scenarioType.getSubTitle()),
                scenarioType.getButtons().stream()
                        .map(button -> new Button(
                                messages.getOrDefault(button.title(), button.title()),
                                button.event(),
                                button.type(),
                                new ButtonAction(
                                        button.action().type(),
                                        messages.getOrDefault(
                                                button.action().payload(),
                                                button.action().payload()))))
                        .collect(Collectors.toList()),
                scenarioType.getIcon(),
                userDecisionContext.getScenarioStatus(),
                scenarioType == ScenarioType.ONBOARDING_FAIL && hasUzumCard,
                userDecisionContext.getScenarioFlags().contains(ScenarioFlag.SHOW_ON_START));
    }

    public void updateUserRestrictions(RestrictionsUpdateEvent updateEvent) {
        UserDecisionContextDto userDecisionContext = userDecisionContextService.createOrUpdateUserDecisionContext(
                updateEvent.getUserId(), updateEvent.getBody(), null);
        if (userDecisionContext == null) {
            return;
        }

        UserInfoDto user = userService.getUser();
        ScenarioType scenarioType = userDecisionContext.getScenarioType();
        Lang lang = Lang.valueOf(userService.getLang(user.userId().toString()).getName());
        Map<String, String> messages = messageService.getMessages(scenarioType.getMessageKeys(), lang);

        NotificationEventDto notificationEventDto =
                createNotificationEventDto(scenarioType, userDecisionContext, null, messages);
        if (notificationEventDto == null) {
            return;
        }

        Button primaryButton = notificationEventDto.buttons().stream()
                .filter(button -> ButtonType.PRIMARY == button.type())
                .findAny()
                .orElse(null);
        EventType eventType = getEventType(userDecisionContext);
        NotificationEvent notificationEvent = notificationEventMapper.toNotificationEvent(
                notificationEventDto, eventType, primaryButton, user.userId().toString(), lang);
        notificationProducer.sendNotificationEvent(notificationEvent, updateEvent.getMessageId());
    }

    private static EventType getEventType(UserDecisionContextDto userDecisionContext) {
        String customerDataProcessing = userDecisionContext.getCustomerDataProcessing();
        return switch (customerDataProcessing) {
            case "isStarted" -> EventType.IDENTITY_DOCUMENT_UPDATE_STARTED;
            case "isCompleted" -> EventType.IDENTITY_DOCUMENT_UPDATE_FINISHED;
            case "isFailed" -> EventType.IDENTITY_DOCUMENT_UPDATE_FAILED;
            default -> throw new IllegalStateException("Unexpected value: " + customerDataProcessing);
        };
    }
}
