package com.userbff.service.verification;

import com.userbff.client.ApelsinClient;
import com.userbff.client.UserVerificationClient;
import com.userbff.exception.FeignClientException;
import com.userbff.exception.ServiceUnavailableException;
import com.userbff.model.common.ResponseData;
import com.userbff.model.verification.VerifyUserRequestDto;
import com.userbff.model.verification.VerifyUserResponseDto;
import feign.FeignException;
import feign.RetryableException;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.sentry.Sentry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import static com.userbff.enums.HttpMethodType.GET;
import static com.userbff.enums.HttpMethodType.POST;
import static com.userbff.enums.RequestStatus.FAILURE;
import static com.userbff.enums.RequestStatus.REQUEST;
import static com.userbff.enums.RequestStatus.SUCCESS;
import static com.userbff.util.MetricsConstants.DESTINATION_TAG;
import static com.userbff.util.MetricsConstants.FEIGN_REQUEST;
import static com.userbff.util.MetricsConstants.FEIGN_REQUEST_TIMER;
import static com.userbff.util.MetricsConstants.HTTP_REQUEST_TYPE_TAG;
import static com.userbff.util.MetricsConstants.HTTP_REQUEST_URL_TAG;
import static com.userbff.util.MetricsConstants.STATUS_TAG;
import static com.userbff.util.MetricsConstants.USER_VERIFICATION_SERVICE;

@Slf4j
@Service
public class UserVerificationService {
    private static final String VERIFICATION_STATUS_V1_URL = "/v1/verification/status";
    private static final String VERIFICATION_VERIFY_V1_URL = "/v1/verification/verify";
    private final UserVerificationClient verificationClient;
    private final ApelsinClient apelsinClient;
    private final MeterRegistry meterRegistry;
    private final Counter verificationStatusRequestCounter;
    private final Counter verificationStatusSuccessCounter;
    private final Counter verificationStatusFailureCounter;
    private final Timer verificationStatusTimer;
    private final Counter verificationVerifyRequestCounter;
    private final Counter verificationVerifySuccessCounter;
    private final Counter verificationVerifyFailureCounter;
    private final Timer verificationVerifyTimer;
    @Value("${feature-toggle.user-verification-from-apelsin}")
    private Boolean isVerificationFromApelsin;

    public UserVerificationService(
            UserVerificationClient verificationClient,
            ApelsinClient apelsinClient,
            MeterRegistry meterRegistry
    ) {
        this.verificationClient = verificationClient;
        this.apelsinClient = apelsinClient;
        this.meterRegistry = meterRegistry;

        verificationStatusRequestCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, USER_VERIFICATION_SERVICE,
                HTTP_REQUEST_TYPE_TAG, GET.name(),
                HTTP_REQUEST_URL_TAG, VERIFICATION_STATUS_V1_URL,
                STATUS_TAG, REQUEST.getStatus()
        );
        verificationStatusSuccessCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, USER_VERIFICATION_SERVICE,
                HTTP_REQUEST_TYPE_TAG, GET.name(),
                HTTP_REQUEST_URL_TAG, VERIFICATION_STATUS_V1_URL,
                STATUS_TAG, SUCCESS.getStatus()
        );
        verificationStatusFailureCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, USER_VERIFICATION_SERVICE,
                HTTP_REQUEST_TYPE_TAG, GET.name(),
                HTTP_REQUEST_URL_TAG, VERIFICATION_STATUS_V1_URL,
                STATUS_TAG, FAILURE.getStatus()
        );
        verificationStatusTimer = meterRegistry.timer(
                FEIGN_REQUEST_TIMER,
                DESTINATION_TAG, USER_VERIFICATION_SERVICE,
                HTTP_REQUEST_TYPE_TAG, GET.name(),
                HTTP_REQUEST_URL_TAG, VERIFICATION_STATUS_V1_URL
        );

        verificationVerifyRequestCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, USER_VERIFICATION_SERVICE,
                HTTP_REQUEST_TYPE_TAG, POST.name(),
                HTTP_REQUEST_URL_TAG, VERIFICATION_VERIFY_V1_URL,
                STATUS_TAG, REQUEST.getStatus()
        );
        verificationVerifySuccessCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, USER_VERIFICATION_SERVICE,
                HTTP_REQUEST_TYPE_TAG, POST.name(),
                HTTP_REQUEST_URL_TAG, VERIFICATION_VERIFY_V1_URL,
                STATUS_TAG, SUCCESS.getStatus()
        );
        verificationVerifyFailureCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, USER_VERIFICATION_SERVICE,
                HTTP_REQUEST_TYPE_TAG, POST.name(),
                HTTP_REQUEST_URL_TAG, VERIFICATION_VERIFY_V1_URL,
                STATUS_TAG, FAILURE.getStatus()
        );
        verificationVerifyTimer = meterRegistry.timer(
                FEIGN_REQUEST_TIMER,
                DESTINATION_TAG, USER_VERIFICATION_SERVICE,
                HTTP_REQUEST_TYPE_TAG, POST.name(),
                HTTP_REQUEST_URL_TAG, VERIFICATION_VERIFY_V1_URL
        );
    }

    public Boolean getVerificationStatus(String userId, String deviceId) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            verificationStatusRequestCounter.increment();
            log.info("invoke UserVerificationService.getVerificationStatus()");

            if (isVerificationFromApelsin) {
                ResponseEntity<ResponseData<Boolean>> response = apelsinClient.isIdentificationCompleted();

                verificationStatusSuccessCounter.increment();
                if (!response.getStatusCode().is2xxSuccessful()) {
                    throw new ServiceUnavailableException(
                            "Apelsin Service returns status: %s on getting verification user status"
                                    .formatted(response.getStatusCode())
                    );
                }

                return response.getBody().data();
            } else {
                ResponseEntity<Boolean> response = verificationClient.getVerificationStatus(userId, deviceId);

                verificationStatusSuccessCounter.increment();
                if (!response.getStatusCode().is2xxSuccessful()) {
                    throw new ServiceUnavailableException(
                            "User-Verification Service returns status: %s on getting verification user status"
                                    .formatted(response.getStatusCode())
                    );
                }

                return response.getBody();
            }
        } catch (RetryableException e) {
            verificationStatusFailureCounter.increment();
            log.error("User-Verification Service is temporarily unavailable, please try again later", e);
            throw new ServiceUnavailableException("User-Verification Service is temporarily unavailable, please try again later", e);
        } catch (FeignException e) {
            verificationStatusFailureCounter.increment();
            log.error("An error occurred while getting verification user status: {}", e.contentUTF8(), e);
            throw new FeignClientException("An error occurred while getting verification user status: " + e.contentUTF8(), e);
        } catch (Exception e) {
            Sentry.captureException(e);
            verificationStatusFailureCounter.increment();
            log.error("An error occurred while getting verification user status: {}", e.getMessage(), e);
            throw e;
        } finally {
            sample.stop(verificationStatusTimer);
        }
    }

    public VerifyUserResponseDto verify(VerifyUserRequestDto verifyUserRequestDto) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            verificationVerifyRequestCounter.increment();
            log.info("invoke UserVerificationService.verify()");

            ResponseEntity<VerifyUserResponseDto> response = verificationClient.verify(verifyUserRequestDto);

            verificationVerifySuccessCounter.increment();
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new ServiceUnavailableException(
                        "User-Verification Service returns status: %d on verifying user"
                                .formatted(response.getStatusCodeValue())
                );
            }

            return response.getBody();
        } catch (RetryableException e) {
            verificationVerifyFailureCounter.increment();
            log.error("User-Verification Service is temporarily unavailable, please try again later", e);
            throw new ServiceUnavailableException("User-Verification Service is temporarily unavailable, please try again later", e);
        } catch (FeignException e) {
            verificationVerifyFailureCounter.increment();
            log.error("An error occurred while verifying user: {}", e.contentUTF8(), e);
            throw new FeignClientException("An error occurred while verifying user: " + e.contentUTF8(), e);
        } catch (Exception e) {
            verificationVerifyFailureCounter.increment();
            Sentry.captureException(e);
            log.error("An error occurred while verifying user: {}", e.getMessage(), e);
            throw e;
        } finally {
            sample.stop(verificationVerifyTimer);
        }
    }
}
