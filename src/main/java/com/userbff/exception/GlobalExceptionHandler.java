package com.userbff.exception;

import com.userbff.model.common.ResponseData;
import io.sentry.Sentry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.time.Instant;

@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {

    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ResponseData<Object>> handleIllegalArgumentException(IllegalArgumentException e) {
        ResponseData<Object> errorResponse = new ResponseData<>(
                e.getMessage(),
                e.getMessage(),
                Instant.now().toEpochMilli(),
                HttpStatus.BAD_REQUEST.name()
        );
        return ResponseEntity
                .badRequest()
                .body(errorResponse);
    }

    @ExceptionHandler(ServiceUnavailableException.class)
    @ResponseStatus(HttpStatus.SERVICE_UNAVAILABLE)
    public ResponseEntity<ResponseData<Object>> handleServiceUnavailableException(ServiceUnavailableException e) {
        Sentry.captureException(e);
        ResponseData<Object> errorResponse = new ResponseData<>(
                e.getMessage(),
                e.getMessage(),
                Instant.now().toEpochMilli(),
                HttpStatus.SERVICE_UNAVAILABLE.name()
        );
        return ResponseEntity
                .badRequest()
                .body(errorResponse);
    }

    @ExceptionHandler(FeignClientException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ResponseData<Object>> handleFeignClientException(FeignClientException e) {
        Sentry.captureException(e);
        ResponseData<Object> errorResponse = new ResponseData<>(
                e.getMessage(),
                e.getMessage(),
                Instant.now().toEpochMilli(),
                HttpStatus.INTERNAL_SERVER_ERROR.name()
        );
        return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(errorResponse);
    }

    /**
     * Ловит все BusinessLogicException, упаковывает детали в поле data,
     * заполняет errorMessage и errorCodeString, возвращает 409.
     */
    @ExceptionHandler(BusinessLogicException.class)
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public ResponseEntity<ResponseData<Object>> handleBusinessLogic(BusinessLogicException ex) {
        ResponseData<Object> errorResponse = new ResponseData<>(
                ex.details,
                ex.message,
                Instant.now().toEpochMilli(),
                ex.errorCode.name()
        );
        return ResponseEntity
                .status(HttpStatus.CONFLICT)
                .body(errorResponse);
    }

    @ExceptionHandler(UnidentifiedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public ResponseEntity<ResponseData<Object>> handleUnidentifiedException(UnidentifiedException ex) {
        ResponseData<Object> errorResponse = new ResponseData<>(
                ex.details,
                ex.message,
                Instant.now().toEpochMilli(),
                ex.errorCode.name()
        );
        return ResponseEntity
                .status(HttpStatus.FORBIDDEN)
                .body(errorResponse);
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ResponseData<Object>> handleGenericException(Exception e) {
        Sentry.captureException(e);
        ResponseData<Object> errorResponse = new ResponseData<>(
                e.getMessage(),
                "An unexpected error occurred: " + e.getMessage(),
                Instant.now().toEpochMilli(),
                HttpStatus.INTERNAL_SERVER_ERROR.name()
        );
        return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(errorResponse);
    }
}

