package com.userbff.exception;

public class UnidentifiedException extends RuntimeException {
    ErrorCode errorCode;
    String message;
    Object details;

    public UnidentifiedException(ErrorCode errorCode, String message, Object details) {
        super(message);

        this.errorCode = errorCode;
        this.message = message;
        this.details = details;
    }

    public UnidentifiedException(ErrorCode errorCode, String message, Object details, Exception e) {
        super(message, e);

        this.errorCode = errorCode;
        this.message = message;
        this.details = details;
    }

    public UnidentifiedException(String userNotVerified) {
    }
}
