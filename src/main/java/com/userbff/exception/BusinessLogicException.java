package com.userbff.exception;

public class BusinessLogicException extends RuntimeException {
    ErrorCode errorCode;
    String message;
    Object details;

    public BusinessLogicException(ErrorCode errorCode, String message, Object details) {
        super(message);

        this.errorCode = errorCode;
        this.message = message;
        this.details = details;
    }

    public BusinessLogicException(ErrorCode errorCode, String message, Object details, Exception e) {
        super(message, e);

        this.errorCode = errorCode;
        this.message = message;
        this.details = details;
    }
}
