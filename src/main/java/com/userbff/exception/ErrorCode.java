package com.userbff.exception;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum ErrorCode {
    REQUIRED_CHECK_FAILED("Требуется дополнительная проверка биометрических данных"),
    USER_CONTEXT_NOT_FOUND_BY_ID("Не найден контекст с id: %s"),
    USER_CONTEXT_NOT_FOUND_BY_USERID("Не найден контекст для пользователя с id: %s"),
    NOT_IMPLEMENTED_YET("Данный функционал еще не реализован"),
    INCORRECT_DATA_FORMAT("Неправильный формат данных"),
    KYC_NO_IDENTIFICATION_REQUEST_FOR_ADDRESS_UPDATE("Не найдена заявка на идентификацию пользователя, в рамках которой требуется ручное обновление адреса");
    private final String description;
}
