package com.userbff.util;

import lombok.experimental.UtilityClass;

@UtilityClass
public class MetricsConstants {
    //---------------------------------------------------------------- Metrics ----------------------------------------------------------------
    public static final String KAFKA_EVENT = "kafka.event";
    public static final String KAFKA_EVENT_TIMER = "kafka.event.timer";
    public static final String FEIGN_REQUEST = "feign.request";
    public static final String FEIGN_REQUEST_TIMER = "feign.request.timer";
    //---------------------------------------------------------------- Tags ----------------------------------------------------------------
    public static final String DESTINATION_TAG = "destination";
    public static final String TOPIC_TAG = "topic";
    public static final String KAFKA_STREAM_TYPE_TAG = "kafka.stream.type";
    public static final String STATUS_TAG = "status";
    public static final String ERROR_INFO_TAG = "error_info";
    public static final String HTTP_REQUEST_URL_TAG = "url";
    public static final String HTTP_REQUEST_TYPE_TAG = "url";
    //---------------------------------------------------------------- Consts ----------------------------------------------------------------
    public static final String USER_RESTRICTIONS_SERVICE = "user-restrictions-service";
    public static final String USER_VERIFICATION_SERVICE = "user-verification-service";
    public static final String APELSIN_SERVICE = "apelsin-service";
    public static final String KYC_SERVICE = "kyc-service";

    public static final String PRODUCER = "producer";
    public static final String CONSUMER = "consumer";
}
