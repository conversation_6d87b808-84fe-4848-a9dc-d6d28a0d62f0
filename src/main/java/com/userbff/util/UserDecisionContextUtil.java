package com.userbff.util;

import com.userbff.enums.ScenarioFeedback;
import com.userbff.enums.ScenarioFlag;
import com.userbff.enums.ScenarioType;
import com.userbff.enums.UserFlag;
import com.userbff.exception.BusinessLogicException;
import com.userbff.exception.ErrorCode;
import com.userbff.model.UserDecisionContextDto;
import com.userbff.model.onboarding.CheckDto;
import com.userbff.model.onboarding.IdentificationState;
import com.userbff.model.restrictions.RestrictionsResponseDto;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@UtilityClass
public class UserDecisionContextUtil {
    public void fillUserDecisionContext(
            @Nonnull
            UserDecisionContextDto userDecisionContext,
            String userId,
            RestrictionsResponseDto restrictions,
            @Nullable
            CheckDto onboardingResponse
    ) {
        mapUserFlags(userDecisionContext, restrictions);

        LocalDate expiryDatePrev = userDecisionContext.getExpiryDate();
        LocalDate expiryDate = Optional.ofNullable(restrictions)
                .map(RestrictionsResponseDto::expiryDate)
                .orElse(null);
        userDecisionContext.setUserId(userId);
        userDecisionContext.setExpiryDatePrev(expiryDatePrev);
        userDecisionContext.setExpiryDate(expiryDate);

        Optional<IdentificationState> identificationStateOpt = Optional.ofNullable(onboardingResponse)
                .map(CheckDto::identificationState);
        if (identificationStateOpt.isPresent() && identificationStateOpt.get() == IdentificationState.FAIL) {
            userDecisionContext.setScenarioType(ScenarioType.ONBOARDING_FAIL);
            userDecisionContext.getScenarioFlags().add(ScenarioFlag.SHOW_ON_START);
        }
        if (identificationStateOpt.isPresent()
                && List.of(IdentificationState.LACK_OF_DATA, IdentificationState.LACK_OF_DATA_HOLD).contains(identificationStateOpt.get())) {
            userDecisionContext.setScenarioType(ScenarioType.DATA_VALIDATION_ERROR);
            userDecisionContext.getScenarioFlags().add(ScenarioFlag.SHOW_ON_START);
        }

        if (restrictions == null) {
            userDecisionContext.setScenarioType(ScenarioType.NONE);
            return;
        }

        if (restrictions.customerDataProcessing() == null) {
            bannerProcessing(userDecisionContext, restrictions);
        } else {
            handleCustomerDataProcessing(userDecisionContext, restrictions, onboardingResponse);
        }
    }

    public static void handleCustomerDataProcessing(
            UserDecisionContextDto userDecisionContext,
            RestrictionsResponseDto restrictions,
            CheckDto onboardingResponse
    ) {
        if (StringUtils.equalsIgnoreCase("isFinished", restrictions.customerDataProcessing())) {
            if (restrictions.resolution() == null) {
                throw new BusinessLogicException(
                        //неправильный формат данных + приложить restrictions в msg + добавить логгирование
                        ErrorCode.INCORRECT_DATA_FORMAT,
                        ErrorCode.INCORRECT_DATA_FORMAT.getDescription(),
                        ErrorCode.INCORRECT_DATA_FORMAT.getDescription()
                );
            } else if (StringUtils.equalsIgnoreCase("failed", restrictions.resolution())) {
                if (onboardingResponse.identificationState() == IdentificationState.FAIL) {
                    userDecisionContext.setScenarioType(ScenarioType.ONBOARDING_FAIL);
                    userDecisionContext.getScenarioFlags().add(ScenarioFlag.SHOW_ON_START);
                } else if (List.of(IdentificationState.SUCCESS, IdentificationState.LIMIT_REACHED,
                                IdentificationState.MODERATION_PINFL, IdentificationState.BLOCKED)
                        .contains(onboardingResponse.identificationState())) {
                    userDecisionContext.setScenarioType(ScenarioType.DATA_VALIDATION_ERROR);
                } else if (List.of(IdentificationState.CREATED, IdentificationState.WAITING,
                        IdentificationState.LACK_OF_DATA).contains(onboardingResponse.identificationState())) {
                    userDecisionContext.setScenarioType(ScenarioType.DATA_IS_UNDER_REVIEW);
                } else if (onboardingResponse.identificationState() == IdentificationState.LACK_OF_DATA_HOLD) {
                    userDecisionContext.setScenarioType(ScenarioType.ADDRESS_UPDATE);
                }
            } else if (StringUtils.equalsIgnoreCase("success", restrictions.resolution())) {
                userDecisionContext.setScenarioType(ScenarioType.NONE);
            }
        } else if (StringUtils.equalsIgnoreCase("isStarted", restrictions.customerDataProcessing())) {
            if (userDecisionContext.getScenarioType() == ScenarioType.ADDRESS_UPDATE) {
                userDecisionContext.setScenarioType(ScenarioType.ADDRESS_IS_UNDER_REVIEW);
            } else {
                userDecisionContext.setScenarioType(ScenarioType.DATA_IS_UNDER_REVIEW);
            }
        }
    }

    private static void mapUserFlags(UserDecisionContextDto userDecisionContext, RestrictionsResponseDto restrictions) {
        if (restrictions == null) {
            return;
        }
        if (restrictions.isResident()) {
            userDecisionContext.getUserFlags().add(UserFlag.RESIDENT);
        }
        if (restrictions.isActive()) {
            userDecisionContext.getUserFlags().add(UserFlag.ACTIVE);
        }
        if (restrictions.hasRegisteredAddress()) {
            userDecisionContext.getUserFlags().add(UserFlag.HAS_REGISTERED_ADDRESS);
        }
        if (restrictions.requiresAddressUpdate()) {
            userDecisionContext.getUserFlags().add(UserFlag.REQUIRES_ADDRESS_UPDATE);
        }
    }

    private static void bannerProcessing(
            UserDecisionContextDto userDecisionContext,
            RestrictionsResponseDto restrictions
    ) {
        if (restrictions.expiryDate().isBefore(LocalDate.now())) {
            if (restrictions.isResident()) {
                userDecisionContext.setScenarioType(ScenarioType.PASSPORT_EXPIRED_RESIDENT);
                userDecisionContext.setClosable(true);
                userDecisionContext.getScenarioFlags().add(ScenarioFlag.SHOW_ON_START);
            } else {
                userDecisionContext.setScenarioType(ScenarioType.PASSPORT_EXPIRED_NONRESIDENT);
                userDecisionContext.setClosable(true);
                userDecisionContext.getScenarioFlags().add(ScenarioFlag.SHOW_ON_START);
            }
        } else if (restrictions.expiryDate().isEqual(LocalDate.now().plusDays(30))
                || restrictions.expiryDate().isEqual(LocalDate.now().plusDays(7))
                || restrictions.expiryDate().isEqual(LocalDate.now().plusDays(2))
                || (
                (restrictions.expiryDate().isEqual(LocalDate.now().plusDays(4))
                        || restrictions.expiryDate().isEqual(LocalDate.now().plusDays(1)))
                        && userDecisionContext.getScenarioFeedback() == ScenarioFeedback.CLOSED
                        && (
                        LocalDate.now().minusDays(4).isBefore(userDecisionContext.getScenarioFeedbackTimestamp())
                                && userDecisionContext.getScenarioFeedbackTimestamp().isBefore(LocalDate.now().minusDays(2))
                )
        )
        ) {
            if (restrictions.isResident()) {
                userDecisionContext.setScenarioType(ScenarioType.PASSPORT_UPDATE_RESIDENT);
                userDecisionContext.setClosable(true);
                userDecisionContext.getScenarioFlags().add(ScenarioFlag.SHOW_ON_START);
            } else {
                userDecisionContext.setScenarioType(ScenarioType.PASSPORT_UPDATE_NONRESIDENT);
                userDecisionContext.setClosable(true);
                userDecisionContext.getScenarioFlags().add(ScenarioFlag.SHOW_ON_START);
            }
        } else if (
                (!restrictions.hasRegisteredAddress() || restrictions.requiresAddressUpdate())
                        && (
                        (restrictions.expiryDate().isAfter(LocalDate.now().plusDays(7))
                                && !restrictions.expiryDate().isEqual(LocalDate.now().plusDays(30)))
                                || ((
                                restrictions.expiryDate().isAfter(LocalDate.now().minusDays(1))
                                        && restrictions.expiryDate().isBefore(LocalDate.now().plusDays(7))
                                        && !restrictions.expiryDate().isEqual(LocalDate.now().plusDays(4))
                                        && !restrictions.expiryDate().isEqual(LocalDate.now().plusDays(2))
                                        && !restrictions.expiryDate().isEqual(LocalDate.now().plusDays(1))
                                        && (userDecisionContext.getScenarioFeedback() != ScenarioFeedback.CLOSED
                                        || userDecisionContext.getScenarioFeedbackTimestamp().isAfter(LocalDate.now().minusDays(2))
                                        || userDecisionContext.getScenarioFeedbackTimestamp().isAfter(LocalDate.now().minusDays(4)))
                        )))
        ) {
            userDecisionContext.setScenarioType(ScenarioType.ADDRESS_UPDATE);
            userDecisionContext.setClosable(true);
            userDecisionContext.getScenarioFlags().add(ScenarioFlag.SHOW_ON_START);
        } else {
            userDecisionContext.setScenarioType(ScenarioType.NONE);
            userDecisionContext.getScenarioFlags().remove(ScenarioFlag.SHOW_ON_START);
        }
    }
}
