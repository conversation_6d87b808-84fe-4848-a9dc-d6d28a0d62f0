package com.userbff.entity;

import com.userbff.enums.ScenarioFeedback;
import com.userbff.enums.ScenarioFlag;
import com.userbff.enums.ScenarioStatus;
import com.userbff.enums.ScenarioType;
import com.userbff.enums.UserFlag;
import jakarta.persistence.Column;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "user_decision_context",
        indexes = {@Index(name = "user_id_index", columnList = "user_id", unique = true)})
public class UserDecisionContext implements Serializable {
    private final static String sequenceName = "user_decision_context_seq";

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = sequenceName)
    @SequenceGenerator(name = sequenceName, sequenceName = sequenceName, allocationSize = 1)
    private Long id;

    @Column(name = "user_id", nullable = false, unique = true)
    private String userId;

    @Column(name = "expiry_date_prev")
    private LocalDate expiryDatePrev;

    @Column(name = "expiry_date")
    private LocalDate expiryDate;

    @Column(name = "scenario_status")
    @Enumerated(EnumType.STRING)
    private ScenarioStatus scenarioStatus;

    @Column(name = "customer_data_processing")
    private String customerDataProcessing;

    @Column(name = "scenario_feedback")
    @Enumerated(EnumType.STRING)
    private ScenarioFeedback scenarioFeedback;

    @Column(name = "scenario_feedback_timestamp")
    private LocalDate scenarioFeedbackTimestamp;

    @Column(name = "scenario_type")
    @Enumerated(EnumType.STRING)
    private ScenarioType scenarioType = ScenarioType.NONE;

    @ElementCollection(fetch = FetchType.EAGER, targetClass = UserFlag.class)
    @Enumerated(EnumType.STRING)
    @Column(name = "user_flags")
    private Set<UserFlag> userFlags = new HashSet<>();

    @ElementCollection(fetch = FetchType.EAGER, targetClass = ScenarioFlag.class)
    @Enumerated(EnumType.STRING)
    @Column(name = "scenario_flags")
    private Set<ScenarioFlag> scenarioFlags = new HashSet<>();

    @CreationTimestamp
    @Column(name = "created_time")
    private LocalDateTime createdTime;
}
