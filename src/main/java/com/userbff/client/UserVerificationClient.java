package com.userbff.client;

import com.userbff.config.UserVerificationConfig;
import com.userbff.model.verification.VerifyUserRequestDto;
import com.userbff.model.verification.VerifyUserResponseDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        name = "userVerificationClient",
        url = "${user-verification.url}/api",
        configuration = UserVerificationConfig.class
)
public interface UserVerificationClient {
    @GetMapping("/v1/verification/status")
    ResponseEntity<Boolean> getVerificationStatus(
            @RequestParam("userId") String userId,
            @RequestParam("deviceId") String deviceId
    );

    @PostMapping("/v1/verification/verify")
    ResponseEntity<VerifyUserResponseDto> verify(@RequestBody VerifyUserRequestDto verifyUserRequestDto);
}
