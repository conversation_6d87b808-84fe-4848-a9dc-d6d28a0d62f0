package com.userbff.client;

import com.userbff.config.ApelsinConfig;
import com.userbff.model.common.ResponseData;
import com.userbff.model.onboarding.CheckDto;
import com.userbff.model.user.UserDeviceDto;
import com.userbff.model.user.UserInfoDto;
import com.userbff.model.user_session.CodeDto;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "apelsinClient", url = "${apelsin.url}/api", configuration = ApelsinConfig.class)
public interface ApelsinClient {
    @GetMapping("/user")
    ResponseEntity<ResponseData<UserInfoDto>> getUser();

    @PatchMapping("/user-activity")
    ResponseEntity<?> updateUserActivity();

    @GetMapping("/onboarding/check")
    ResponseEntity<ResponseData<CheckDto>> getOnboardingCheck();

    @PostMapping("/identification-external")
    ResponseEntity<?> identificationByExternal(@RequestBody CodeDto codeDto);

    @GetMapping("/identification/global/check")
    ResponseEntity<ResponseData<Boolean>> isIdentificationCompleted();

    @GetMapping("/users/{userId}/devices")
    List<UserDeviceDto> getUserDevices(@PathVariable("userId") String userId);
}
