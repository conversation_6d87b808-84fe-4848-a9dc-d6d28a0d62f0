package com.userbff.client;

import com.userbff.config.KYCConfig;
import com.userbff.model.kyc.KYCBaseResponseDto;
import com.userbff.model.kyc.KYCUserAddressUpdateRequestDto;
import com.userbff.model.kyc.regions.RegionsDistrictResponseDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

//@FeignClient(
//        name = "kycClient",
//        url = "${kyc.url}/api",
//        configuration = KYCConfig.class
//)
public interface KYCClient {
//    @GetMapping("/v1/abs/dictionaries/regions-districts")
//    ResponseEntity<RegionsDistrictResponseDto> getRegionsDistrictDictionary();
//
//    @PostMapping("/v1/customer/registration-address")
//    ResponseEntity<KYCBaseResponseDto> updateRegistrationAddress(@RequestBody KYCUserAddressUpdateRequestDto request);
}
