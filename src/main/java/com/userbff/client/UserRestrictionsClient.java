package com.userbff.client;

import com.userbff.config.UserRestrictionsConfig;
import com.userbff.model.restrictions.RestrictionsResponseDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(
        name = "userRestrictionsClient",
        url = "${user-restrictions.url}",
        configuration = UserRestrictionsConfig.class
)
public interface UserRestrictionsClient {
    @GetMapping("/user-restrictions/{userId}")
    ResponseEntity<RestrictionsResponseDto> getUserRestrictions(@PathVariable("userId") String userId);
}
